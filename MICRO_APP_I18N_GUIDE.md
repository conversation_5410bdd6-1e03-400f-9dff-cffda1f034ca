# 微前端国际化同步指南

## 概述

本指南说明如何在微前端架构中实现主应用和子应用之间的国际化同步，确保所有应用使用统一的语言设置。

## 主要问题解决

### 1. 登录过期处理优化
- **问题**: 子应用中的请求返回登录过期错误时，直接弹出错误信息并跳转登录页
- **解决**: 改进了错误处理逻辑，增加了延迟跳转和更好的用户体验

### 2. 语言同步问题
- **问题**: 子应用的国际化设置没有跟随主应用，导致界面语言不一致
- **解决**: 通过 qiankun 的全局状态管理实现语言同步

## 主应用配置

### 1. 全局状态初始化 (`src/utils/actions.js`)

```javascript
import { initGlobalState } from 'qiankun'
import { getStoreLang } from '@/utils/langUtils'

const initialState = {
  language: getStoreLang(), // 当前语言
  user: null, // 用户信息
  token: null // 令牌信息
}

const actions = initGlobalState(initialState)

// 导出更新方法
export const updateLanguage = (language) => {
  actions.setGlobalState({ language })
}
```

### 2. 微前端配置 (`src/config/microAppConfig.js`)

为每个子应用传递必要的 props：

```javascript
props: {
  msg: "我是来自主应用的值-react",
  actions,
  appIdentifier: "cms",
  // 传递主应用的语言和认证信息
  language: getStoreLang(),
  token: getToken(),
  // 传递主应用的国际化实例
  mainAppI18n: window.i18n,
}
```

### 3. 国际化同步工具 (`src/utils/microAppI18nSync.js`)

提供了完整的国际化同步机制，包括：
- 初始化同步
- 语言变化事件处理
- 子应用配置辅助函数

## 子应用集成

### 1. 在子应用的入口文件中

```javascript
import { initSubAppI18n } from 'path/to/microAppI18nSync';

// 在子应用挂载时
export async function mount(props) {
  // 初始化国际化同步
  const i18nConfig = initSubAppI18n(yourI18nInstance, props);
  
  // 使用同步后的语言设置
  console.log('当前语言:', i18nConfig.currentLanguage);
  
  // 渲染应用...
}
```

### 2. 监听语言变化

```javascript
// 在子应用中监听主应用的语言变化
if (props && props.actions) {
  props.actions.onGlobalStateChange((state) => {
    if (state.language) {
      // 更新子应用的语言
      yourI18nInstance.changeLanguage(state.language);
    }
  });
}
```

## 错误处理改进

### 登录过期处理 (`src/utils/request.js`)

```javascript
if (code === RespCode.LOGIN_EXPIREDIRED || code === RespCode.ERROR_EXPIREDIRED) {
  if (!isRelogin.show) {
    isRelogin.show = true;
    
    // 显示登录过期提示
    toast.warn(i18n.t(`errorCode.${RespCode.LOGIN_EXPIREDIRED}`));
    
    // 清除认证信息
    removeToken();
    sessionStorage.clear();
    
    // 延迟跳转，给用户时间看到提示信息
    setTimeout(() => {
      // 检查当前是否在子应用中
      const currentPath = window.location.pathname;
      const isMicroApp = currentPath.startsWith('/cms-app') || 
                       currentPath.startsWith('/retail-ai-app') || 
                       currentPath.startsWith('/e-price-tag-app');
      
      window.location.href = "/login";
      isRelogin.show = false;
    }, 1500);
  }
  
  return Promise.reject({
    code: code,
    message: i18n.t(`errorCode.${RespCode.LOGIN_EXPIREDIRED}`)
  });
}
```

## 使用流程

### 1. 主应用启动时
1. 初始化全局状态，包含当前语言
2. 启动国际化同步机制
3. 注册微前端应用，传递必要的 props

### 2. 语言切换时
1. 用户在主应用中切换语言
2. 更新本地存储和主应用国际化
3. 通过全局状态通知所有子应用
4. 子应用接收到变化后同步更新

### 3. 子应用加载时
1. 接收主应用传递的 props
2. 初始化国际化同步
3. 设置为主应用的当前语言
4. 监听后续的语言变化

## 注意事项

1. **子应用必须支持**: 子应用需要实现相应的国际化监听和切换逻辑
2. **错误处理**: 确保子应用正确处理主应用传递的错误信息
3. **性能考虑**: 语言切换时避免不必要的重新渲染
4. **兼容性**: 确保所有子应用都支持相同的语言代码格式

## 调试建议

1. 检查浏览器控制台是否有相关错误信息
2. 验证 qiankun 的全局状态是否正确传递
3. 确认子应用是否正确接收和处理 props
4. 测试语言切换时的同步效果

## 扩展功能

可以基于此机制扩展更多功能：
- 主题同步
- 用户权限同步
- 其他全局状态同步
