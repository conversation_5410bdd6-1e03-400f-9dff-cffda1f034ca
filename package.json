{"name": "retail-ai-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env QIANKUN=1 vite", "dev:standalone": "vite", "build": "cross-env NODE_ENV=production QIANKUN=1 vite build", "build:standalone": "cross-env NODE_ENV=production vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@iconify/react": "^5.2.1", "@material-ui/core": "^4.12.3", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.155", "@mui/material": "^5.11.8", "@mui/x-data-grid": "^6.9.1", "@mui/x-date-pickers": "^5.0.20", "@react-google-maps/api": "^2.18.1", "@reduxjs/toolkit": "^2.2.6", "@testing-library/jest-dom": "^5.16.5", "antd": "^4.24.8", "axios": "^1.3.2", "crypto-js": "^4.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "formik": "^2.4.6", "framer-motion": "^7.3.6", "i18next": "^22.4.9", "i18next-browser-languagedetector": "^7.0.1", "i18next-xhr-backend": "^3.2.2", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jwt-decode": "^3.1.2", "less": "^4.2.0", "lodash": "^4.17.21", "material-ui-popup-state": "^5.0.9", "moment": "^2.30.1", "notistack": "^2.0.8", "prop-types": "^15.8.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-element-to-jsx-string": "^15.0.0", "react-file-base64": "^1.0.3", "react-geocode": "^0.2.3", "react-i18next": "^12.1.5", "react-image-lightbox": "^5.1.4", "react-phone-input-2": "^2.15.1", "react-redux": "^8.0.5", "react-router-dom": "^6.4.2", "react-spinners": "^0.13.8", "react-toastify": "^11.0.5", "react-window": "^1.8.10", "redux": "^4.2.0", "resize-observer-polyfill": "^1.5.1", "simplebar-react": "^2.4.1", "typeface-roboto": "^1.1.13", "vite-plugin-qiankun": "^1.0.15", "yup": "^1.4.0"}, "devDependencies": {"@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "fast-glob": "^3.3.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "unplugin-icons": "^22.1.0", "vite": "^5.4.10", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0"}}