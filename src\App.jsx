import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import "./css/App.css";
import Routes from "@/router/routers.js";
import onRouteBefore from "@/router/onRouteBefore.js";
import Loader from "@/router/Component/Loader.jsx";
import RouterWaiter from "@/router/routerWaiter/index.jsx";
import { SnackbarProvider } from "notistack";

import "antd/dist/antd.min.css";
import "virtual:svg-icons-register";
import { getUserMenus } from "@/services/common.js";
import { ToastContainer, Zoom } from "react-toastify";
import { useDispatch } from "react-redux";
import { useDispatchMenu } from "@/store/hooks/menu";

function App() {
  const dispatch = useDispatch();
  const { stateSetMenu } = useDispatchMenu();

  useEffect(async () => {
    let menus = null;
    menus = await getUserMenus({ applicationCode: "ZT" }).then(async (res) => {
      menus = res.data;

      if (menus.length == 0) {
        toast.error("您没有任何权限，请联系管理员");
        setTimeout(() => {
          window.location.href = "/application/center";
        }, 1000);
      } else if (res.code == "AUTH000019") {
        toast.error(res.message);
        setTimeout(() => {
          window.location.href = "/application/center";
        }, 1000);
      } else {
        dispatch(stateSetMenu(menus));
      }
    });
  }, []);

  return (
    <BrowserRouter basename="/retail-ai-app">
      <SnackbarProvider
        maxSnack={3}
        autoHideDuration={3000}
        anchorOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
        style={{
          marginTop: "300px",
        }}>
        <ToastContainer
          position="top-center"
          style={{
            fontSize: "16px",
          }}
          autoClose={5000}
          hideProgressBar
          newestOnTop={false}
          closeOnClick
          rtl={false}
          limit={5}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
          transition={Zoom}
        />
        <RouterWaiter
          routes={Routes}
          loading={<Loader />}
          onRouteBefore={onRouteBefore}
        />
      </SnackbarProvider>
    </BrowserRouter>
  );
}

export default App;
