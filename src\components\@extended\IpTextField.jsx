import React from 'react'
/* eslint-disable react/prop-types */
import  { useCallback, useMemo, useState } from 'react';
import { OutlinedInput } from '@mui/material';
export default function IpTextField(props) {
    // 设置 IP 的基本格式
    const base = useMemo(() => '_._._._', []);
    // 设置当前光标的位置
    const setSelection = useCallback((element, start, end) => {
        element.selectionStart = start;
        if (end === undefined || end === null) element.selectionEnd = start;
        else element.selectionEnd = end;
    }, []);
    return (
        <>
            <OutlinedInput
                id={props.id ? props.id : 'ipInput'}
                type="text"
                name={props.name}
                value={props.value}
                placeholder={'请输入IP地址'}
                onChange={(e) => {
                    // self 保存当前输入框本身信息, 使用const防止修改
                    // val 保存改变后的输入框中的值，可以通过它直接修改输入框的信息
                    // location 保存输入框当前的光标信息
                    const self = e.target;
                    let val = self.value;
                    let location = self.selectionStart;
                    // ;
                    if (val.match(/^(\d){1}$/)) {
                        // 如果 全选然后用一个数字覆盖输入时
                        // 显示 ${NUM}._._._
                        val = `${val}._._._`;
                    } else if (val.match(/^(.){1}$/)) {
                        // 如果全选，用任意一个非空白字符覆盖时
                        // 显示base状态
                        val = base;
                    } else if (
                        // 如果拷贝过来一个合法IP时
                        val.match(/^(((\d){0,3})\.((\d){0,3})\.((\d){0,3})\.((\d){0,3}))(?=(_._._._))/) ||
                        val.match(/(?<=(_._._._))(((\d){0,3})\.((\d){0,3})\.((\d){0,3})\.((\d){0,3}))/)
                    ) {
                        // 显示该IP
                        val = val.replace(base, '');
                    } else if (
                        // 如果 输入后，不符合IP规则(允许存在_)，
                        !val.match(
                            /^((_)|((_)?(\d){0,3}(_)?))\.((_)|((_)?(\d){0,3}(_)?))\.((_)|((_)?(\d){0,3}(_)?))\.((_)|((_)?(\d){0,3}(_)?))$/
                        )
                    ) {
                        if (val.match(/[a-zA-Z~!@#$%^&*()+=`{}[\]\\|;':",/<>?。，、；：“”；‘’《》？！￥…（） -]/)) {
                            // 如果输入了以上字符(不属于IP规则内的)，对输入光标的位置做调整；其他复杂输入不做判断
                            // 因为不会应用这些字符, 所以对光标位置做退格处理
                            // 当 location 是 0 时, 不做退格处理
                            if (location !== 0) location--;
                        }

                        if (val.match(/[0-9]{4}/)) {
                            // 正常输入情况下，匹配到连续四个数字大致有三种情况
                            // 1. 将 . 去掉，但程序会把点再补回来，直接让光标自动退一个即可
                            // 2. 用户主动输入了 4个 数字, 会将最后一个输入的数字去掉，光标保持原位
                            // 3. 用户粘贴了 4个 数字, 因为此时粘贴过来的是一个非法值, 所以直接 过掉
                            if (
                                val.match(
                                    /^((_)|(_)?(\d){0,4}(_)?)\.((_)|(_)?(\d){0,4}(_)?)\.((_)|(_)?(\d){0,4}(_)?)\.((_)|(_)?(\d){0,4}(_)?)$/
                                )
                            )
                                // 上述第2种情况, 主动输入的第四个数字不会被应用
                                // 所以做退格处理
                                location--;
                        }

                        if (self.value === '') {
                            // 当前值为空时，修改为 base 状态
                            self.value = base;

                            // setIP(self.value);
                            if (props.onChange !== undefined) {
                                props.onChange(self.value, e);
                            }

                            setSelection(self, location);

                            return;
                        }

                        // 舍弃用户当前修改, 调整光标位置
                        // 这里注意, 一定要先修改输入框的value, 再更新state
                        self.value = props.value;
                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }
                        setSelection(self, location);

                        return;
                    }

                    if (val.match(/__/)) {
                        // 不允许连续下划线出现
                        location--;
                        self.value = props.value;
                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }
                        setSelection(self, location);
                        return;
                    }

                    // 当输入 合法时，做一系列检查和调整
                    // ipArr 数组 存放IP的四个字段
                    // IPSub 记录当前正在验证字段的位置
                    let ipArr = val.split('.');
                    let IPSub = 0;

                    for (let i = 0; i < 4; i++) {
                        // 字段位置可以理解为, 该字段最后一个数字的位置
                        IPSub += ipArr[i].length;
                        // 如果该字段中有 "_", 则要去掉 "_"
                        if (ipArr[i].match(/^((_){1}(\d){1,3})$/)) {
                            // 当 "_" 在该字段的首位, 两种情况需要移动光标
                            if (IPSub === location) {
                                // 光标在该字段末位
                                location--;
                            } else if (IPSub - ipArr[i].length === location - (self.value.length - props.value.length)) {
                                // 光标在该字段 数字的中间位置时
                                // 可以通过 粘贴 _1 来测试
                                location--;
                            }

                            // 将 "_" 去掉
                            ipArr[i] = ipArr[i].replaceAll('_', '');
                            // 去掉 "_" 后, 该字段长度减一
                            IPSub--;
                        }

                        if (ipArr[i].match(/^((\d){1,3}(_){1})$/)) {
                            // 如果下划线在数字后面
                            // 如果 光标在该字段末位, 光标需要退格
                            if (location === IPSub) location--;
                            ipArr[i] = ipArr[i].replaceAll('_', '');

                            IPSub--;
                        }

                        if (ipArr[i].match(/^((_){1}(\d){1,3}(_){1})$/)) {
                            // 如果前后都有下划线
                            // 如果 光标在该字段末位, 光标需要退格2格
                            if (location === IPSub) location -= 2;
                            // 如果光标在 数字中间, 光标退格一位
                            else if (location > IPSub - ipArr[i].length && location < IPSub) location--;
                            ipArr[i] = ipArr[i].replaceAll('_', '');

                            IPSub--;
                            IPSub--;
                        }

                        // 字段为空 补齐 _
                        if (ipArr[i] === '') ipArr[i] = '_';

                        // 如果字段以 0 开头，则去零
                        if (ipArr[i].match(/^0[0-9]$/)) {
                            ipArr[i] = String(Number(ipArr[i]));
                            if (location !== 0) if (location !== IPSub - ipArr[i].length - 1) location--;
                        }
                        if (ipArr[i].match(/^0[1-9]{2}$/)) {
                            ipArr[i] = String(Number(ipArr[i]));
                            if (location !== 0) if (location !== IPSub - ipArr[i].length - 1) location--;
                        }
                        if (ipArr[i].match(/^(00)[0-9]$/)) {
                            ipArr[i] = String(Number(ipArr[i]));

                            location -= 2;
                        }
                        // _ 和 0 清理掉后

                        if (location === IPSub) {
                            // 如果已经输入了三个数字，自动跳到下一个字段
                            if (ipArr[i].length === 3) location++;
                        }
                        // 别忘了给 IPSub 补上 . 的位置
                        IPSub++;
                    }

                    // 为 输入框赋新值
                    self.value = ipArr.join('.');
                    setSelection(self, location);
                    // setIP(self.value);
                    if (props.onChange !== undefined) {
                        props.onChange(self.value, e);
                    }
                    // setIP(self.value);
                }}
                onMouseOver={(e) => {
                    // 在鼠标覆盖时, 如果原有值为空, 显示base 状态
                    const self = e.target;

                    if (self.value === '' || self.value === undefined || self.value === null) {
                        self.value = base;
                    }

                    if (props.onMouseOver !== undefined) {
                        props.onMouseOver(e);
                    }
                }}
                onMouseLeave={(e) => {
                    // 当鼠标离开时, 如果为 base 状态, 清除内容
                    const self = e.target;

                    if (self === document.activeElement) return;

                    if (self.value === base) {
                        self.value = '';
                    }

                    if (props.value === base) {
                        // setIP("");
                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }
                    }

                    if (props.onMouseLeave !== undefined) {
                        props.onMouseLeave(e);
                    }
                }}
                onMouseUp={(e) => {
                    // 当鼠标点击抬起时
                    // 如果当前为 内容为空, 自动补充 base 状态
                    // 如果当前是 base 状态, 那么光标移动到 0 位置
                    const self = e.target;
                    if (self.value === '' || self.value === undefined || self.value === null) {
                        self.value = base;

                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }

                        setSelection(self, 0);
                    }

                    if (self.value === base) {
                        self.value = base;

                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }

                        setSelection(self, 0);
                    }

                    if (props.onMouseUp !== undefined) {
                        props.onMouseUp(e);
                    }
                }}
                onFocus={(e) => {
                    // 输入框聚焦时, 如果值为空, 显示base 状态
                    // 如果 值为 base 状态, 光标自动置为首位
                    const self = e.target;

                    if (self.value === '' || self.value === undefined || self.value === null) {
                        self.value = base;

                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }

                        setSelection(self, 0);
                    }

                    if (self.value === base) {
                        self.value = base;

                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }

                        self.selectionStart = 0;
                        self.selectionEnd = 0;
                    }

                    if (props.onFocus !== undefined) {
                        props.onFocus(e);
                    }
                }}
                onBlur={(e) => {
                    // 当前输入框失去焦点时，若输入框内容为 base 状态，就修改成 空状态
                    const self = e.target;
                    if (self.value === base) {
                        self.value = '';
                        // setIP(self.value);
                        if (props.onChange !== undefined) {
                            props.onChange(self.value, e);
                        }
                    }

                    if (props.onBlur !== undefined) {
                        props.onBlur(e);
                    }
                }}
                // inBoxClass={"forbid-typewriting"}
                onKeyDown={(e) => {
                    const self = e.target;

                    const curLocation = self.selectionStart;

                    if (e.key === 'Backspace') {
                        // 当按下退格键时, 如果是base 状态, 光标置为首位
                        if (self.value === base) {
                            self.selectionStart = 0;
                            self.selectionEnd = 0;
                        }
                    }
                    if (e.key === 'v') {
                        // 当按下 v 键时, 如果是base 状态, 光标置为首位
                        // 主要目的是为了 CV 操作
                        if (self.value === base) {
                            self.selectionStart = 0;
                            self.selectionEnd = 0;
                        }
                    }
                    if (e.key === '.') {
                        // 当按下点时, 自动跳转到下一个字段
                        let relocation = self.value.indexOf('.', curLocation);
                        self.selectionStart = relocation;
                        self.selectionEnd = relocation;
                    }

                    if (props.onKeyDown !== undefined) {
                        props.onKeyDown(e);
                    }
                }}
                fullWidth
                error={props.error}
                // error={Boolean(deviceFormik.touched.name && deviceFormik.errors.name)}
            />
        </>
    );
}
