import React, { useContext } from "react";
import RightViewLayout from "./RighViewLayout";
import DataAccessMenu from "./DataAccessContolMenu";
import { useState } from "react";
import { useEffect } from "react";
import { getAllApplications } from "../services/DataAccessService";
import SelectApplication from "../pages/data_access_control/SelectApplication";
import DeviceDataAccess from "../pages/data_access_control/DeviceDataAccessControl";
import RetailClientDataAccess from "../pages/data_access_control/RetailClientDataAccess";
import PrincipalsAccess from "../pages/data_access_control/PrincipalsAccess";
import { useNavigate, useParams } from "react-router";
import { useTranslation } from "react-i18next";

export default function DataAccessLayout(props) {
  const navigate = useNavigate();
  const { index } = useParams();
  const { t } = useTranslation();
  const [applications, setApplications] = useState([]);
  const [application, setApplication] = useState({});
  const [selectedIndex, setSelectedIndex] = useState("");
  const [clear, setClear] = useState(false);

  useEffect(() => {
    getAllApplications().then((response) => {
      if (response?.data?.data && response?.data?.code === "LVLI0000") {
        setApplications(response.data.data);
      }
    });
  }, []);

  const handleChange = (value) => {
    setApplication(value);
    setClear(true);
  };

  const handleMenuClick = (index) => {
    setSelectedIndex(index);
  };

  return (
    <RightViewLayout {...props} type="table" title={t('LVLDAC0001')}>
      <SelectApplication options={applications} onSelect={handleChange} />
      <DataAccessMenu onSelect={handleMenuClick} />
      {selectedIndex === "Device" && (
        <DeviceDataAccess application={application} clear={clear} />
      )}
      {selectedIndex === "RetailClient" && (
        <RetailClientDataAccess application={application} clear={clear}/>
      )}
      {selectedIndex === "Principals" && (
        <PrincipalsAccess application={application} clear={clear} />
      )}
    </RightViewLayout>
  );
}
