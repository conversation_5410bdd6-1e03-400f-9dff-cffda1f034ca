import React from 'react'
// material-ui
import { <PERSON>ton, CardMedia, Link, Stack, Typography } from "@mui/material";
import LinearProgress from "@mui/material/LinearProgress";
// project import
import MainCard from "@/components/MainCard";
import "./navCard.less";
// assets
import avatar from "@/assets/images/users/test.png";

import AnimateButton from "@/components/@extended/AnimateButton";

// ==============================|| DRAWER CONTENT - NAVIGATION CARD ||============================== //

const NavCard = () => (
  <MainCard sx={{ bgcolor: "grey.50", m: 3 }} className="card">
    <Stack alignItems="center" spacing={2.5}>
      {/* <div className="memoryBox">
                <div>asd</div>
                <div>
                    <div>剩余内存</div>
                    <div>28/32GB</div>
                </div>
            </div>
            <div style={{ width: '100%' }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={0} style={{ marginBottom: '10px' }}>
                    <div>进度</div>
                    <div>90%</div>
                </Stack>
                <LinearProgress variant="determinate" value={90} />
            </div> */}
      <CardMedia component="img" image={avatar} sx={{ width: 112 }} />
      <Stack alignItems="center">
        <Typography variant="h5">剩余内存</Typography>
        <Typography variant="h6" color="secondary">
          28/32GB
        </Typography>
      </Stack>
      <AnimateButton>
        {/* <Button component={Link} target="_blank" href="https://mantisdashboard.io" variant="contained" color="success" size="small">
                    Pro
                </Button> */}
      </AnimateButton>
    </Stack>
  </MainCard>
);

export default NavCard;
