import React, { useState, useEffect } from "react";

import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { activeItem } from "@/store/reducers/menu";
import { useTheme } from "@mui/material/styles";
import { List, ListItemButton, Collapse } from "@mui/material";

import SvgIcon from "@/components/SvgIcon";

function NavCollapse({ item }) {
  const theme = useTheme();
  const { openItem } = useSelector((state) => state.menu);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { pathname } = useLocation();

  // 点击事件处理
  const handleClick = (e) => {
    if (item?.children) {
      setIsOpen(!isOpen);
      dispatch(activeItem({ openItem: [item.code] }));
    } else {
      navigate(item.path);
    }
  };

  // 路径匹配更新状态
  useEffect(() => {
    if (pathname?.startsWith(item.path)) {
      dispatch(activeItem({ openItem: [item.code] }));
      setIsOpen(true);
    } else if (
      item?.children?.some((childItem) => pathname === childItem.path)
    ) {
      // 修改: 确保子菜单路径匹配时，父菜单保持展开
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [pathname, item.path, item.children]);

  return (
    <List component="div" disablePadding>
      <ListItemButton
        sx={{
          // pl: 5,
          m: 1,
          ml: 0.5,
          mr: 0.5,
          // mb: 0.3,
          // padding: "3px 12px 3px 12px",
          "&:hover": {
            borderRadius: "6px",
            bgcolor: theme.palette.action.hover,
          },
          // 修改: 确保父级菜单字体加粗且保持黑色
          fontWeight: openItem?.includes(item.code) ? "bold" : "normal",
          color: "inherit",
        }}
        className="flex items-center py-2 pl-[24px] gap-2"
        onClick={handleClick}>
        <div>
          {<SvgIcon width="16px" height="16px" localIcon={item?.icon} />}
        </div>
        <div
          style={{
            font: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
          }}
          // style={{ font: "normal normal normal 14px/18px Proxima Nova" }}
          className="text-sm flex-auto overflow-hidden text-ellipsis min-w-[0]">
          {item.name}
        </div>
        {item?.children &&
          (isOpen ? (
            <ExpandLess className="text-sm" />
          ) : (
            <ExpandMore className="text-sm" />
          ))}
      </ListItemButton>
      <Collapse in={isOpen} timeout="auto" unmountOnExit className="bg-zinc-50">
        <List component="div" disablePadding>
          {item?.children?.map((childItem) => (
            <ListItemButton
              key={childItem.code}
              className="flex items-center"
              sx={{
                // pl: 4,
                m: 1,
                ml: 0.2,
                mr: 0.2,
                mb: 0.3,
                paddingLeft: "50px",
                paddingTop: "8px",
                paddingBottom: "8px",
                // padding: "3px 12px 3px 48px",
                // 子级菜单字体保持黑色
                bgcolor:
                  pathname === childItem.path
                    ? theme.palette.primary.light
                    : "transparent",
                "&:hover": {
                  borderRadius: "6px",
                  bgcolor:
                    pathname === childItem.path
                      ? theme.palette.primary.main
                      : theme.palette.action.hover,
                },
                fontWeight: pathname === childItem.path ? "bold" : "normal",
                color:
                  pathname === childItem.path
                    ? theme.palette.primary.main
                    : "inherit",
                // borderLeft: `5px solid ${
                //   pathname === childItem.path
                //     ? theme.palette.primary.main
                //     : "transparent"
                // }`,
              }}
              onClick={() => navigate(childItem.path)}
              selected={pathname === childItem.path}>
              {/* <div className={"pr-1"}>{getIcons(item?.icon)}</div> */}
              <div
                style={{
                  font: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                }}
                // style={{ font: "normal normal normal 14px/18px Proxima Nova" }}
                className="text-sm flex-auto overflow-hidden text-ellipsis min-w-[0]">
                {childItem.name}
              </div>
              {/* <Typography
                // className="flex-auto overflow-hidden text-ellipsis min-w-[0]"
                variant="h6"
                sx={{
                  flex:"auto",
                  display: "flex",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  minWidth: "0",
                  // ml: 0.5,
                  // color: isSelected ? "#474B4F" : "rgba(71, 75, 79, 0.6)",
                  font: `normal normal normal 14px/18px Proxima Nova`,
                  // fontWeight: isSelected ? 500 : 400,
                }}
              >
                {childItem.name}
              </Typography> */}
              {/* <ListItemIcon>{getIcons(childItem?.icon)}</ListItemIcon> */}
              {/* <ListItemText>{childItem.name}</ListItemText> */}
            </ListItemButton>
          ))}
        </List>
      </Collapse>
    </List>
  );
}

export default NavCollapse;

// <List component="div" disablePadding>
//   <ListItemButton
//     sx={{
//       // pl: 5,
//       m: 1,
//       ml: 0.5,
//       mr: 0.5,
//       // mb: 0.3,
//       // padding: "3px 12px 3px 12px",
//       "&:hover": {
//         borderRadius: "6px",
//         bgcolor: theme.palette.action.hover,
//       },
//       // 修改: 确保父级菜单字体加粗且保持黑色
//       fontWeight: openItem?.includes(item.code) ? "bold" : "normal",
//       color: "inherit",
//     }}
//     className="flex items-center py-2 pl-[24px] gap-2"
//     onClick={handleClick}>
//     <div>
//       {<SvgIcon width="16px" height="16px" localIcon={item?.icon} />}
//     </div>
//     <div
//       style={{
//         font: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
//       }}
//       // style={{ font: "normal normal normal 14px/18px Proxima Nova" }}
//       className="text-sm flex-auto overflow-hidden text-ellipsis min-w-[0]">
//       {item.name}
//     </div>
//     {item?.children &&
//       (isOpen ? (
//         <ExpandLess className="text-sm" />
//       ) : (
//         <ExpandMore className="text-sm" />
//       ))}
//   </ListItemButton>
//   <Collapse in={isOpen} timeout="auto" unmountOnExit>
//     <List component="div" disablePadding>
//       {item?.children?.map((childItem) => (
//         <ListItemButton
//           key={childItem.code}
//           className="flex items-center"
//           sx={{
//             // pl: 4,
//             m: 1,
//             ml: 0.2,
//             mr: 0.2,
//             mb: 0.3,
//             paddingLeft: "50px",
//             paddingTop: "8px",
//             paddingBottom: "8px",
//             // padding: "3px 12px 3px 48px",
//             // 子级菜单字体保持黑色
//             bgcolor:
//               pathname === childItem.path
//                 ? theme.palette.primary.light
//                 : "transparent",
//             "&:hover": {
//               borderRadius: "6px",
//               bgcolor:
//                 pathname === childItem.path
//                   ? theme.palette.primary.main
//                   : theme.palette.action.hover,
//             },
//             fontWeight: pathname === childItem.path ? "bold" : "normal",
//             color:
//               pathname === childItem.path
//                 ? theme.palette.primary.main
//                 : "inherit",
//             // borderLeft: `5px solid ${
//             //   pathname === childItem.path
//             //     ? theme.palette.primary.main
//             //     : "transparent"
//             // }`,
//           }}
//           onClick={() => navigate(childItem.path)}
//           selected={pathname === childItem.path}>
//           {/* <div className={"pr-1"}>{getIcons(item?.icon)}</div> */}
//           <div
//             style={{
//               font: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
//             }}
//             // style={{ font: "normal normal normal 14px/18px Proxima Nova" }}
//             className="text-sm flex-auto overflow-hidden text-ellipsis min-w-[0]">
//             {childItem.name}
//           </div>
//           {/* <Typography
//             // className="flex-auto overflow-hidden text-ellipsis min-w-[0]"
//             variant="h6"
//             sx={{
//               flex:"auto",
//               display: "flex",
//               overflow: "hidden",
//               textOverflow: "ellipsis",
//               minWidth: "0",
//               // ml: 0.5,
//               // color: isSelected ? "#474B4F" : "rgba(71, 75, 79, 0.6)",
//               font: `normal normal normal 14px/18px Proxima Nova`,
//               // fontWeight: isSelected ? 500 : 400,
//             }}
//           >
//             {childItem.name}
//           </Typography> */}
//           {/* <ListItemIcon>{getIcons(childItem?.icon)}</ListItemIcon> */}
//           {/* <ListItemText>{childItem.name}</ListItemText> */}
//         </ListItemButton>
//       ))}
//     </List>
//   </Collapse>
// </List>
