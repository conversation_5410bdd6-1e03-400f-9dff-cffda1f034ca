/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import PropTypes from "prop-types";
import React from "react";
import { createElement, forwardRef, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@mui/material/styles";
import {
  Avatar,
  Chip,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import * as Icons from "@ant-design/icons";
import { activeItem } from "@/store/reducers/menu";

const Iconfont = (props) => {
  const { icon } = props;
  if (Icons[icon] == undefined) {
    return "";
  } else {
    return React.createElement(Icons[icon]);
  }
};

const NavItem = ({ item, level }, props) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { drawerOpen, openItem } = useSelector((state) => state.menu);
  const { pathname } = useLocation();

  let itemTarget = "_self";
  if (item.target) {
    itemTarget = "_blank";
  }

  let listItemProps = {
    component: forwardRef((props, ref) => (
      <Link ref={ref} {...props} to={item.path || " "} target={itemTarget} />
    )),
  };
  if (item?.external) {
    listItemProps = {
      component: "a",
      href: item.path || " ",
      target: itemTarget,
    };
  }

  const itemHandler = (code) => {
    dispatch(activeItem({ openItem: [code] }));
  };

  const itemIcon = item.icon ? (
    <Iconfont
      icon={item.icon}
      style={{ fontSize: drawerOpen ? "2rem" : "2.25rem" }}
    />
  ) : (
    false
  );

  // todo 用于解决刷新页面，菜单选中样式无法对应
  useEffect(() => {
    if (pathname?.includes(item.path)) {
      dispatch(activeItem({ openItem: [item.code] }));
    }
  }, [pathname]);

  const isSelected = openItem.findIndex((code) => code === item.code) > -1;

  const textColor = "text.primary";
  const iconSelectedColor = "primary.main";

  return (
    <ListItemButton
      {...listItemProps}
      disabled={item.disabled}
      onClick={() => itemHandler(item.code)}
      selected={isSelected}
      sx={{
        zIndex: 1201,
        // borderRadius: "6px",
        // font: `normal normal normal 16px/19px Proxima Nova`,
        pl: drawerOpen ? `${level * 28}px` : 1.5,
        py: !drawerOpen && level === 1 ? 1.25 : 1,
        ...(drawerOpen && {
          "&:hover": {
            bgcolor: "rgba(145 158 171 / 0.08)",
          },
          "&.Mui-selected": {
            bgcolor: "rgba(122, 193, 67,0.2)",
            // borderLeft: `4px solid ${theme.palette.primary.main}`,
            color: iconSelectedColor,
            "&:hover": {
              color: iconSelectedColor,
              bgcolor: "rgba(122, 193, 67, 0.26)",
            },

            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              bottom: 0,
              width: "4px", // 设置边框宽度
              backgroundColor: theme.palette.primary.main,
            },
          },
        }),
        ...(!drawerOpen && {
          "&:hover": {
            bgcolor: "transparent",
          },
          "&.Mui-selected": {
            "&:hover": {
              bgcolor: "transparent",
            },
            bgcolor: "transparent",
          },
        }),
      }}>
      <ListItemIcon
        sx={{
          minWidth: 28,
          color: isSelected ? iconSelectedColor : textColor,
          ...(!drawerOpen && {
            borderRadius: 1.5,
            width: 36,
            height: 36,

            alignItems: "center",
            justifyContent: "center",
            "&:hover": {
              bgcolor: "secondary.lighter",
            },
          }),
          ...(!drawerOpen &&
            isSelected && {
              bgcolor: "primary.lighter",
              "&:hover": {
                bgcolor: "primary.lighter",
              },
            }),
        }}>
        {itemIcon}
      </ListItemIcon>

      {(drawerOpen || (!drawerOpen && level !== 1)) && (
        <ListItemText
          primary={
            <Typography
              variant="h6"
              sx={{
                color: isSelected ? "#474B4F" : "rgba(71, 75, 79, 0.6)",
                ml: 2.4,
                font: `normal normal normal 14px/18px Proxima Nova`,
                fontWeight: isSelected ? 500 : 400,
              }}>
              {item.name}
            </Typography>
          }
        />
      )}
      {(drawerOpen || (!drawerOpen && level !== 1)) && item.chip && (
        <Chip
          color={item.chip.color}
          variant={item.chip.variant}
          size={item.chip.size}
          label={item.chip.label}
          avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
        />
      )}
    </ListItemButton>
  );
};

NavItem.propTypes = {
  item: PropTypes.object,
  level: PropTypes.number,
};

export default NavItem;
