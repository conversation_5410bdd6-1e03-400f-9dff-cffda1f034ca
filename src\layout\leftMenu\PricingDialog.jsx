import React from "react";
//套餐显示弹窗
export const PricingDialog = ({ open, setOpen }) => {
  const [data, setData] = useState([]);
  const { t } = useTranslation();
  const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    "& .MuiDialogContent-root": {
      padding: theme.spacing(2),
    },
    "& .MuiDialogActions-root": {
      padding: theme.spacing(1),
    },
  }));

  const bodyStyle = {
    boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
  };

  const titleStyle = {
    fontWeight: 700,
    textAlign: "center",
    color: "#FFF",

    fontFamily: "Myriad Pro",
  };

  const freeTextStyle = {
    fontWeight: 700,
    textAlign: "center",
    color: "#FFF",
  };

  return (
    <React.Fragment>
      <BootstrapDialog
        onClose={() => setOpen(false)}
        open={open}
        // maxWidth={2000}
        sx={{
          maxWidth: { xs: 1400, lg: 2000 },
        }}
        overflowX={"auto"}
      >
        <DialogTitle
          sx={{ m: 0, p: 2, fontFamily: "Myriad Pro" }}
          fontSize={26}
          fontWeight={700}
        >
          {t("LVL0030")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={() => setOpen(false)}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          <Box display={"flex"} justifyContent={"space-around"} width={1200}>
            {data?.map((item, index) => {
              const backgroundStyle = {
                0: "linear-gradient(to top, #141e30, #243b55)",
                1: "linear-gradient(to top, #1da1fe, #ffafbd)",
                2: "linear-gradient(to bottom, #fffcdc, #ffafbd)",
                3: "linear-gradient(to top, #f7971e, #fffcdc)",
              };
              return (
                <Box sx={bodyStyle} key={index}>
                  <Box
                    sx={{
                      background: backgroundStyle[index % 4], // 根据index选择不同的背景色
                    }}
                    height={140}
                  >
                    <Box
                      sx={titleStyle}
                      fontSize={26}
                      whiteSpace={"nowrap"}
                      lineHeight={3}
                    >
                      {item.name}
                    </Box>
                    <Box sx={freeTextStyle} fontSize={28} lineHeight={2}>
                      Free
                    </Box>
                  </Box>
                  <Box
                    ml={2}
                    mr={1}
                    mt={3}
                    width={260}
                    height={160}
                    fontSize={20}
                    fontWeight={700}
                    fontFamily={"Myriad Pro"}
                  >
                    <span dangerouslySetInnerHTML={{ __html: item.desc }} />
                  </Box>
                </Box>
              );
            })}
          </Box>
        </DialogContent>
      </BootstrapDialog>
    </React.Fragment>
  );
};
