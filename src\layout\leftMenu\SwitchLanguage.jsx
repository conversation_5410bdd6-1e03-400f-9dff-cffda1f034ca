import React, { useState } from "react";
import Typography from "@mui/material/Typography";
import Popover from "@mui/material/Popover";
import {
  usePopupState,
  bindTrigger,
  bindPopover,
} from "material-ui-popup-state/hooks";
import { Avatar, Box, MenuItem } from "@mui/material";
import { useEffect } from "react";
import CommonUtil from "../../util/CommonUtils";
import UserService from "../../services/UserService";
import DefaultUserPic from "@/assets/images/default_user_pic.svg?react";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import SidebarMenuItem from "@/layout/components/SidebarMenuItem";
import LanguageMenu from "@/assets/images/menu_language.svg?react";
import AppLanguage from "@/lang/AppLanguages";
import { useTranslation } from "react-i18next";

export default function SwitchLanguage() {
  // const [user, setUser] = useState({});

  const { t } = useTranslation();
  // useEffect(() => {
  //   let user = CommonUtil.decodeToken();
  //   if (user) {
  //     getUser(user?.id, true);
  //   }
  // }, []);

  // const getUser = async (id, loadProfileImage) => {
  //   await UserService.getUserDetails(id, loadProfileImage).then((res) => {
  //     localStorage.setItem("USER_INFO", JSON.stringify(res?.data?.data));
  //     setUser(res?.data?.data);
  //   });
  // };

  const switchLanguage = (code) => {
    AppLanguage.setLanguage(code);
    window.location.reload();
  };

  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });
  return (
    <div>
      <Box
        display="flex"
        alignItems={"center"}
        justifyContent={"flex-start"}
        style={{ cursor: "pointer", height: "33px" }}
        {...bindTrigger(popupState)}
        sx={{ px: { xs: 0, md: 0 }, my: { xs: 0, md: 1 } }}
      >
        <Box flexGrow={1}>
          <SidebarMenuItem
            link=""
            label={t("LVLDB0008")}
            isActive={false}
            activeMenu={LanguageMenu}
            inActiveMenu={LanguageMenu}
          />
        </Box>
        <Box py={0.5} px={2} sx={{ display: { xs: "none", md: "flex" } }}>
          <Typography variant="menuItem">
            {popupState.isOpen ? (
              <KeyboardArrowLeftIcon fontSize="small" />
            ) : (
              <KeyboardArrowRightIcon fontSize="small" />
            )}
          </Typography>
        </Box>
      </Box>
      <Popover
        {...bindPopover(popupState)}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}
      >
        {/* <MenuItem onClick={popupState.close}>View Profile</MenuItem>
        <MenuItem onClick={popupState.close}>Edit Profile</MenuItem> */}
        <MenuItem
          onClick={() => {
            switchLanguage("en");
            popupState.close();
          }}
        >
          {t("LVL0011")}
        </MenuItem>
        <MenuItem
          onClick={() => {
            switchLanguage("zh");
            popupState.close();
          }}
        >
          {t("LVL0017")}
        </MenuItem>
        {/* <MenuItem
          onClick={() => {
            switchLanguage("ge");
            popupState.close();
          }}
        >
          {t("LVL0033")}
        </MenuItem>
        <MenuItem
          onClick={() => {
            switchLanguage("es");
            popupState.close();
          }}
        >
          {t("LVL0032")}
        </MenuItem> */}
      </Popover>
    </div>
  );
}
