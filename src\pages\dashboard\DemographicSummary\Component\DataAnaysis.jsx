import { <PERSON>, Grid, Button } from "@mui/material";
import React, { useState, useEffect, useRef, useContext } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import FormatPreValue from "./FormatPreValue";
import { VisitorDemographicContext } from "..";
import { timeBoxStyle } from "../../css/DemoGraphicDataAnaysis"; // 引入样式对象
import GradientBox from "@/components/GradientBox/index.jsx";
import { useTranslation } from "react-i18next";
import { parseNumber } from "@/util/parseNumber";

function DataAnaysis() {
  const { t } = useTranslation();
  const [openTime, setOpenTime] = useState(false);
  const [hiddenButton, setHiddenButton] = useState(1);
  const { visitorDemographic, setSelectedTime, selectTime } = useContext(
    VisitorDemographicContext
  );

  useEffect(() => {
    if (selectTime == 1) {
      setHiddenButton(1);
    } else {
      setHiddenButton(2);
    }
  }, [selectTime]);

  const box1Ref = useRef(null);
  const box2Ref = useRef(null);

  const handleScroll = (e) => {
    if (e.target === box1Ref.current) {
      // 如果是第一个盒子的滚动，应用到第二个盒子上
      box2Ref.current.scrollTop = e.target.scrollTop;
    } else if (e.target === box2Ref.current) {
      // 如果是第二个盒子的滚动，应用到第一个盒子上
      box1Ref.current.scrollTop = e.target.scrollTop;
    }
  };

  return (
    <Grid container xs={12}>
      <Grid item>
        <DateButton
          hiddenButton={hiddenButton}
          setOpenTime={setOpenTime}
          openTime={openTime}
          visitorDemographic={visitorDemographic}
          box1Ref={box1Ref}
          handleScroll={handleScroll}
          setSelectedTime={setSelectedTime}></DateButton>
      </Grid>

      <Grid item sx={{ flexGrow: 1, pr: 2, mt: 2, ml: 2, pb: 2 }}>
        <DataTable
          visitorDemographic={visitorDemographic}
          box2Ref={box2Ref}
          handleScroll={handleScroll}></DataTable>
      </Grid>
    </Grid>
  );
}

export default DataAnaysis;

const DataTable = ({ visitorDemographic, box2Ref, handleScroll }) => {
  const { t } = useTranslation();
  const columns = [
    {
      field: "F0",
      headerName: "F0",
      age: t("PCS69") + " 0 -11",
    },
    {
      field: "F1",
      headerName: "F1",
      age: t("PCS69") + " 12 - 17",
    },
    {
      field: "F2",
      headerName: "F2",
      age: t("PCS69") + " 18 -34",
    },
    {
      field: "F3",
      headerName: "F3",
      age: t("PCS69") + " 35 -54",
    },
    {
      field: "F4",
      headerName: "F4",
      age: t("PCS69") + " 55+",
    },

    {
      field: "M0",
      headerName: "M0",
      age: t("PCS69") + " 0 -11",
    },
    {
      field: "M1",
      headerName: "M1",
      age: t("PCS69") + " 12 - 17",
    },
    {
      field: "M2",
      headerName: "M2",
      age: t("PCS69") + "18 -34",
    },
    {
      field: "M3",
      headerName: "M3",
      age: t("PCS69") + "35 -54",
    },
    {
      field: "M4",
      headerName: "M4",
      age: t("PCS69") + " 55+",
    },
    {
      field: "total",
      headerName: t("PCS11"),
    },
  ];

  const cellStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
  };

  return (
    <React.Fragment>
      <GradientBox
        style={{
          padding: "5px",
          minHeight: "30px",
          lineHeight: "30px",
          border: "1px solid #ccc",
        }}>
        <Grid columns={{ xs: 11, sm: 11, md: 11 }} container>
          {columns.map((item, index) => {
            return (
              <Grid style={cellStyle} item xs={1}>
                <span>{item?.headerName}</span>
                <span>{item?.age}</span>
              </Grid>
            );
          })}
        </Grid>
      </GradientBox>
      <Grid
        ref={box2Ref}
        onScroll={handleScroll}
        style={{
          maxHeight: "500px",
          overflow: "auto",
        }}>
        {Object.keys(visitorDemographic)?.map((row, index) => {
          const data = visitorDemographic[row];
          return (
            <GradientBox
              style={{
                padding: "5px",
                border: "1px solid #ccc",
                marginTop: "15px",
              }}>
              <Grid columns={{ xs: 11, sm: 11, md: 11 }} container>
                {data?.map((item, index) => {
                  if (item.totalCount !== null) {
                    return (
                      <Grid
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        item
                        xs={1}>
                        <span
                          style={{
                            fontSize: "16px",
                          }}>
                          {parseNumber(item.totalCount)}
                        </span>
                        <span
                          style={{
                            paddingLeft: "5px",
                            paddingBottom: "10px",
                          }}>
                          <FormatPreValue value={item.totalGrowth}>
                            {item.totalGrowth}
                          </FormatPreValue>
                        </span>
                      </Grid>
                    );
                  } else {
                    return (
                      <Grid
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        item
                        xs={1}>
                        <span
                          style={{
                            fontSize: "16px",
                          }}>
                          {item.percentage}%
                        </span>
                        <span
                          style={{
                            paddingLeft: "5px",
                            paddingBottom: "10px",
                          }}>
                          <FormatPreValue value={item.growth}></FormatPreValue>
                        </span>
                      </Grid>
                    );
                  }
                })}
              </Grid>
            </GradientBox>
          );
        })}
      </Grid>
    </React.Fragment>
  );
};

{
  /* 右侧时间展示栏 */
}

const DateButton = ({
  hiddenButton,
  setOpenTime,
  openTime,
  visitorDemographic,
  handleScroll,
  box1Ref,
  setSelectedTime,
}) => {
  const { t } = useTranslation();

  // Handle the selection of time value (2, 4, 6 hours)
  const handleSelectValue = (value) => {
    setSelectedTime(value);
    setOpenTime(false);
  };

  // Handle conditional rendering of buttons
  const isHiddenButtonVisible = hiddenButton == 1;

  return (
    <Grid container sx={{ ml: 2, mt: 2 }}>
      <Grid item>
        {/* If hiddenButton is 1, show time selection button */}
        {isHiddenButtonVisible ? (
          <Button
            variant="contained"
            sx={{
              height: "60px",
              borderRadius: "35px",
              fontWeight: 700,
              position: "relative",
              width: "200px",
            }}
            onClick={() => setOpenTime((prevOpenTime) => !prevOpenTime)}>
            <Box>{t("PCS65")}</Box>
            <Box>{openTime ? <ExpandMoreIcon /> : <ExpandLessIcon />}</Box>
          </Button>
        ) : (
          // If hiddenButton is not 1, show the alternate button
          <Button
            variant="contained"
            sx={{
              height: "60px",
              borderRadius: "35px",
              fontWeight: 700,
              marginTop: "15px",
              position: "relative",
              width: "200px",
            }}
            fontSize={26}>
            {t("PCS32")}
          </Button>
        )}

        {/* Display time selection dropdown when openTime is true */}
        {openTime && isHiddenButtonVisible && (
          <Grid
            container
            sx={{
              display: "flex",
              flexDirection: "column",
              background: "burlywood",
              zIndex: 11111111111,
              position: "absolute",
              color: "#fff",
              textAlign: "center",
              fontWeight: 700,
              width: "190px",
              marginLeft: "6px",
              alignItems: "center",
              justifyContent: "center",
              minHeight: "35px",
              lineHeight: "35px",
              borderRadius: "15px",
              marginTop: "15px",
            }}>
            {[1, 2, 3].map((value) => (
              <Grid
                item
                key={`time-option-${value}`} // Make key unique
                onClick={() => handleSelectValue(value)}
                sx={{
                  cursor: "pointer",
                  padding: "8px 0",
                  fontSize: "16px",
                  width: "140px",
                  borderBottom: "4px solid #fff",

                  ":hover": {
                    background: "burlywood", // 修改背景颜色
                    boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.3)", // 增加阴影效果
                    color: "#FFF",
                    fontWeight: 700,
                    width: "190px",
                    borderRadius: "10px",
                  },

                  ":active": {
                    background: "burlywood", // 修改背景颜色
                    transform: "scale(0.95)", // 缩放效果
                  },
                }}>
                {value * 2} Hours
              </Grid>
            ))}
          </Grid>
        )}

        {/* Render visitor demographics with scrollable container */}
        {!isHiddenButtonVisible && visitorDemographic && (
          <div ref={box1Ref} onScroll={handleScroll}>
            {Object.keys(visitorDemographic).map((item, index) => {
              return (
                <Box
                  key={item + index} // Use item as a key, assuming it's unique
                  sx={{
                    textAlign: "center",
                    margin: "auto",
                    border: "1px solid #ccc",
                    marginTop: "15px",
                    cursor: "pointer",
                    borderRadius: "8px",
                    width: "190px",
                    minHeight: "40px", // Fixed typo (minHeight instead of minHight)
                    lineHeight: "40px",
                  }}
                  fontSize={20}
                  ml={4}>
                  {item.replace(/-/g, "/")}
                </Box>
              );
            })}
          </div>
        )}
      </Grid>
    </Grid>
  );
};
