import { useEffect, useState, useContext } from "react";
import { Box, Grid, Typography } from "@mui/material";
import { VisitorDemographicContext } from "..";
import { useTranslation } from "react-i18next";
import { parseNumber } from "@/util/parseNumber";
import React from "react";
import GradientBox from "@c/GradientBox";
function FourSquares() {
  const [ageBracket, setAgeBracket] = useState("");
  const { t } = useTranslation();
  const [leastAgeBracket, setLeastAgeBracket] = useState("");

  const calculateAgeBracket = (demographic) => {
    switch (demographic) {
      case "F0":
      case "M0":
        return "Age 0 -11";
      case "F1":
      case "M1":
        return "Age 12 - 17";
      case "F2":
      case "M2":
        return "Age 18 -34";
      case "F3":
      case "M3":
        return "Age 35 -54";
      case "F4":
      case "M4":
        return "Age 55+";
      default:
        return "Age 0 -11";
    }
  };

  const {
    highestDemographic,
    highestDemographicCount,
    highestTime,
    highestTimeCount,
    leastDemographic,
    leastDemographicCount,
    leastTime,
    leastTimeCount,
  } = useContext(VisitorDemographicContext);
  useEffect(() => {
    setAgeBracket(calculateAgeBracket(highestDemographic));
    setLeastAgeBracket(calculateAgeBracket(leastDemographic));
  }, [highestDemographic, leastDemographic]);

  return (
    <div className="grid grid-cols-24 grid-flow-col gap-x-4 w-full">
      {/* 最高访问时间 */}
      <div className="col-span-6 bg-[#fbe2e5] rounded-xl shadow-lg p-4  flex flex-row justify-between">
        <div>
          <h6 className="text-sm font-medium">{t("PCS58")}</h6>
          <h3 className="text-xl font-bold">
            {highestTime?.replace(/-/g, "/")}
          </h3>
        </div>
        <div className="bg-[#ee587c] text-white rounded-lg w-20 h-18 flex flex-col items-center justify-center">
          <h2 className="text-3xl font-medium">
            {parseNumber(highestTimeCount)}
          </h2>
          <h6 className="text-sm">{t("PCS59")}</h6>
        </div>
      </div>

      {/* 最低访问时间 */}
      <div className="col-span-6 bg-[#fef3dd] rounded-xl shadow-lg p-4  flex flex-row justify-between">
        <div>
          <h6 className="text-sm font-medium">{t("PCS58")}</h6>
          <h3 className="text-xl font-bold">{leastTime?.replace(/-/g, "/")}</h3>
        </div>
        <div className="bg-[#f39379] text-white rounded-lg w-20 h-18 flex flex-col items-center justify-center">
          <h2 className="text-2xl font-medium">
            {parseNumber(leastTimeCount)}
          </h2>
          <h6 className="text-sm">{t("PCS59")}</h6>
        </div>
      </div>

      {/* 最高访问人群 */}
      <div className="col-span-6 bg-[#dcfde8] rounded-xl shadow-lg p-4  flex flex-row justify-between">
        <div>
          <h6 className="text-sm font-medium">{t("PCS61")}</h6>
          <div className="flex items-center">
            <h3 className="text-xl font-bold mr-2">{highestDemographic}</h3>
            <span className="text-sm">
              {highestDemographic?.charAt(0) === "M" ? t("PCS62") : t("PCS70")}(
              {ageBracket})
            </span>
          </div>
        </div>
        <div className="bg-[#65d957] text-white rounded-lg w-20 h-18 flex flex-col items-center justify-center">
          <h2 className="text-2xl font-medium">
            {parseNumber(highestDemographicCount)}
          </h2>
          <h6 className="text-sm">{t("PCS59")}</h6>
        </div>
      </div>

      {/* 最低访问人群 */}
      <div className="col-span-6 bg-[#f5e9ff] rounded-xl shadow-lg p-4  flex fflex-row justify-between">
        <div>
          <h6 className="text-sm font-medium">{t("PCS63")}</h6>
          <div className="flex items-center">
            <h3 className="text-xl font-bold mr-2">{leastDemographic}</h3>
            <span className="text-sm">
              {leastDemographic?.charAt(0) === "M" ? t("PCS62") : t("PCS70")}(
              {leastAgeBracket})
            </span>
          </div>
        </div>
        <div className="bg-[#c082f9] text-white rounded-lg w-20 h-18 flex flex-col items-center justify-center">
          <h2 className="text-2xl font-medium">
            {parseNumber(leastDemographicCount)}
          </h2>
          <h6 className="text-sm">{t("PCS59")}</h6>
        </div>
      </div>
    </div>
  );
}

export default FourSquares;

//  <div className="w-full sm:w-1/4 bg-[#fbe2e5] rounded-xl shadow-lg flex flex-row">
//       <div
//         className="sm:w-3/4"
//         style={{
//           margin: "20px",
//         }}>
//         <h6 className="text-sm font-medium">{t("PCS58")}</h6>
//         <h3 className="text-xl font-bold">
//           {highestTime?.replace(/-/g, "/")}
//         </h3>
//       </div>

//       <div
//         className="bg-[#ee587c] text-white rounded-lg sm:w-1/4 text-center h-20"
//         style={{
//           margin: "20px",
//         }}>
//         <h2 className="text-3xl font-medium">
//           {parseNumber(highestTimeCount)}
//         </h2>
//         <h6 className="text-sm">{t("PCS59")}</h6>
//       </div>
//     </div>

//     <div className="w-full sm:w-1/4 bg-[#fef3dd] rounded-xl shadow-lg flex flex-row">
//       <div
//         className="sm:w-3/4"
//         style={{
//           margin: "20px",
//         }}>
//         <h6 className="text-sm font-medium">{t("PCS58")}</h6>
//         <h3 className="text-xl font-bold">{leastTime?.replace(/-/g, "/")}</h3>
//       </div>

//       <div
//         className="bg-[#f39379] text-white rounded-lg sm:w-1/4 text-center h-20"
//         style={{
//           margin: "20px",
//         }}>
//         <h2 className="text-2xl font-medium">
//           {parseNumber(leastTimeCount)}
//         </h2>
//         <h6 className="text-sm">{t("PCS59")}</h6>
//       </div>
//     </div>

//     <div className="w-full sm:w-1/4 bg-[#dcfde8] rounded-xl shadow-lg flex flex-row">
//       <div
//         className="sm:w-3/4"
//         style={{
//           margin: "20px",
//         }}>
//         <h6 className="text-sm font-medium mb-2">{t("PCS61")}</h6>
//         <div className="flex items-center mb-2">
//           <h3 className="text-xl font-bold mr-2">{highestDemographic}</h3>
//           <span className="text-sm">
//             {highestDemographic?.charAt(0) === "M" ? t("PCS62") : t("PCS70")}(
//             {ageBracket})
//           </span>
//         </div>
//       </div>

//       <div
//         className="bg-[#65d957] text-white rounded-lg sm:w-1/4 text-center h-20"
//         style={{
//           margin: "20px",
//         }}>
//         <h2 className="text-2xl font-medium">
//           {parseNumber(highestDemographicCount)}
//         </h2>
//         <h6 className="text-sm">{t("PCS59")}</h6>
//       </div>
//     </div>

//     <div className="w-full sm:w-1/4 bg-[#f5e9ff] rounded-xl shadow-lg flex flex-row">
//       <div
//         className="sm:w-3/4"
//         style={{
//           margin: "20px",
//         }}>
//         <h6 className="text-sm font-medium mb-2">{t("PCS63")}</h6>
//         <div className="flex items-center mb-2">
//           <h3 className="text-xl font-bold mr-2">{leastDemographic}</h3>
//           <span className="text-sm">
//             {leastDemographic?.charAt(0) === "M" ? t("PCS62") : t("PCS70")}(
//             {leastAgeBracket})
//           </span>
//         </div>
//       </div>

//       <div
//         className="bg-[#c082f9] text-white rounded-lg sm:w-1/4 text-center h-20"
//         style={{
//           margin: "20px",
//         }}>
//         <h2 className="text-2xl font-medium">
//           {parseNumber(leastDemographicCount)}
//         </h2>
//         <h6 className="text-sm">{t("PCS59")}</h6>
//       </div>
//     </div>
