import React, { useState, useContext, useEffect } from "react";
import { Box, Grid, Typography, Button } from "@mui/material";
import { VisitorDemographicContext } from "../index";
import { useTranslation } from "react-i18next";
import { parseNumber } from "@/util/parseNumber";
import GradientBox from "@/components/GradientBox";
import DialogTime from "./CurrentTime";
import CompareTime from "./CompareTime";
import SelectOutlet from "../../Component/SelectOutlet";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
function SelectTimeRange(props) {
  const { t } = useTranslation();
  const [curOpen, setCurOpen] = useState(false);
  const [perOpen, setPerOpen] = useState(false);
  const [storeOpen, setStoreOpen] = useState(false); //所有门店下拉弹窗

  const {
    startTimes,
    setStartTimes,
    endTimes,
    setEndTimes,
    timeType,
    startValueTime,
    endValueTime,
    setStartValueTime,
    setEndValueTime,
    selectTime,
    storeName,
    setStoreName,
    setSelectTime,
    setTimeType,
    loadData,
    totalNumber,
    storeList,
    isShowAllStore,
  } = useContext(VisitorDemographicContext);

  return (
    <React.Fragment>
      <Grid container xs={12} m={2}>
        <Grid container xs={10} spacing={2}>
          <GradientBox>
            <Box
              sx={{
                p: 2,
              }}
              onClick={() => {
                setCurOpen(true);
              }}>
              <Typography
                style={{
                  fontSize: "14px",
                  color: "rgba(121,121,121,1)",
                }}>
                {t("PCS03")}
              </Typography>
              <Grid
                sx={{
                  display: "flex",
                  mt: 2,
                }}>
                <Typography
                  style={{
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "rgba(14,14,14,1)",
                    whiteSpace: "nowrap",
                  }}>
                  {startTimes} - {endTimes}
                </Typography>

                <Box ml={2}>
                  {open ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </Box>
              </Grid>
            </Box>
          </GradientBox>

          <GradientBox
            style={{
              marginLeft: "15px",
            }}>
            <Grid
              item
              xs={4}
              sx={{
                p: 2,
              }}
              onClick={() => {
                setPerOpen(true);
              }}>
              <Typography
                style={{
                  fontSize: "14px",
                  color: "rgba(121,121,121,1)",
                }}>
                {t("PCS03")}
              </Typography>
              <Grid
                sx={{
                  display: "flex",
                  mt: 2,
                }}>
                <Typography
                  style={{
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "rgba(14,14,14,1)",
                    whiteSpace: "nowrap",
                  }}>
                  {startValueTime} - {endValueTime}
                </Typography>

                <Box ml={2}>
                  {open ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </Box>
              </Grid>
            </Grid>
          </GradientBox>

          <GradientBox
            style={{
              marginLeft: "15px",
            }}>
            <Grid
              item
              xs={4}
              sx={{
                p: 2,
              }}
              onClick={() => {
                setStoreOpen(true);
              }}>
              <Typography
                style={{
                  fontSize: "14px",
                  color: "rgba(121,121,121,1)",
                  width: "200px",
                }}>
                {t("PCS08")}
              </Typography>
              <Grid
                sx={{
                  display: "flex",
                  mt: 2,
                }}>
                <Typography
                  style={{
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "rgba(14,14,14,1)",
                    whiteSpace: "nowrap",
                  }}>
                  {storeName == "All" ? "All" : storeName?.name}
                </Typography>

                <Box ml={2}>
                  {storeOpen ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </Box>
              </Grid>
            </Grid>
          </GradientBox>

          <GradientBox
            style={{
              marginLeft: "15px",
            }}>
            <Button
              variant="contained"
              onClick={() => loadData()}
              sx={{
                borderRadius: "8px",
                fontWeight: 700,
                fontSize: "18px",
                width: "100%",
                height: "100%",
              }}>
              <span dangerouslySetInnerHTML={{ __html: t("PCS10") }}></span>
            </Button>
          </GradientBox>
        </Grid>

        <Grid container xs={2} justifyContent="end">
          <Grid
            item
            xs={2}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "end",
              fontSize: "22px",
              fontWight: 700,
              color: "#878787",
            }}>
            <span dangerouslySetInnerHTML={{ __html: t("PCS64") }} />
          </Grid>
          <Grid
            item
            xs={8}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: "#fff",
              textAlign: "center",
              borderRadius: "15px",
              fontSize: "28px",
              fontWeight: 700,
              marginLeft: "10px",
            }}>
            <span
              style={{
                background: `linear-gradient(to right, #71b652, #3599d7)`,
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}>
              {parseNumber(totalNumber)}
            </span>
          </Grid>
        </Grid>
      </Grid>

      {storeName && (
        <SelectOutlet
          storeOpen={storeOpen}
          setStoreOpen={setStoreOpen}
          setStoreName={setStoreName}
          storeName={storeName}
          storeList={storeList}
          isShowAllStore={isShowAllStore}
        />
      )}

      {/* 第一个时间选择组件 */}
      <DialogTime
        open={curOpen}
        timeType={timeType}
        setOpen={setCurOpen}
        setStartTimes={setStartTimes}
        endTimes={endTimes}
        setTimeType={setTimeType}
        setEndTimes={setEndTimes}
        setStartValueTime={setStartValueTime}
        setEndValueTime={setEndValueTime}
        selectTime={selectTime}
        setSelectTime={setSelectTime}></DialogTime>

      {/* 第二个时间范围选择组件 */}
      <CompareTime
        open1={perOpen}
        endTimes={endTimes}
        setOpen1={setPerOpen}
        startTimes={startTimes}
        selectTime={selectTime}
        setStartValueTime={setStartValueTime}
        setEndValueTime={setEndValueTime}></CompareTime>
    </React.Fragment>
  );
}

export default SelectTimeRange;
