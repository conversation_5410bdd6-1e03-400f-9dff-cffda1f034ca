import React, { useState, useEffect, createContext, useRef } from "react";
import { Box, Card, Grid } from "@mui/material";
import { getDemographic } from "@/services/dashboard.js";
import SelectTimeRange from "./Component/SelectTimeRange.jsx";
import LineCharts from "./Component/LineChart.jsx";
import AgeLineCharts from "./Component/AgeLineCharts.jsx";
import { headerStyle } from "../css/peopleCounting.js";
import { useSnackbar } from "notistack";
import FourSquares from "./Component/FourSquares.jsx";
import DataAnaysis from "./Component/DataAnaysis.jsx";
import { contentStyle } from "../css/Demographic.js";
import { useTranslation } from "react-i18next";
import { useStateUserInfo } from "@/hooks/user";
import GradientBox from "@c/GradientBox";
import {
  getStoreData,
  handlerTimeData,
} from "../PeopleCountingSummary/Component/GetTime.js";
// 创建一个 Context
export const VisitorDemographicContext = createContext();
export default function DemographicFun() {
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const [startTimes, setStartTimes] = useState(""); //第一个时间组件选中的开始时间
  const [endTimes, setEndTimes] = useState(""); //第一个时间组件选中的结束时间
  const [startValueTime, setStartValueTime] = useState(""); //第二个时间组件选中的开始时间
  const [endValueTime, setEndValueTime] = useState(""); //第二个时间组件选中的结束时间
  const [selectTime, setSelectTime] = useState(2); //选择的时间范围 类型 1 为7天 2 为5天
  const [timeType, setTimeType] = useState(2); //默认的时间类型 day / mouth
  const [storeName, setStoreName] = useState(null); //选中的门店名称
  const [selectedTime, setSelectedTime] = useState(1);
  const [isInitialized, setIsInitialized] = useState(false); //判断是否初始化加载接口
  const [totalNumber, setTotalNumber] = useState(0); // Total Captures

  /**
   * 四个方块绑定数据
   */
  const [highestDemographic, setHighestDemographic] = useState("");
  const [highestDemographicCount, setHighestDemographicCount] = useState(0);
  const [highestTime, setHighestTime] = useState("");
  const [highestTimeCount, setHighestTimeCount] = useState(0);
  const [leastDemographic, setLeastDemographic] = useState("");
  const [leastDemographicCount, setLeastDemographicCount] = useState(0);
  const [leastTime, setLeastTime] = useState("");
  const [leastTimeCount, setLeastTimeCount] = useState(0);
  const [storeList, setStoreList] = useState([]);
  //visitorDemographic 数据绑定
  const [visitorDemographic, setVisitorDemographic] = useState([]);

  // LineChart 组件数据Gender Distribution 模块
  const [genderData, setGenderData] = useState([]);
  // Age Distribution 模块数据
  const [ageData, setAgeData] = useState([]);

  const [isShowAllStore, setIsShowAllStore] = useState(false); //判断是都为超级管理员

  const rolesData = JSON.parse(sessionStorage.getItem("USER_INFO"));

  useEffect(() => {
    handlerTimeData(
      setStartTimes,
      setEndTimes,
      setEndValueTime,
      setStartValueTime
    );
    if (isInitialized === true) return;
    setIsInitialized(true);
  }, [isInitialized]);

  useEffect(() => {
    if (rolesData && rolesData?.employeeType !== 2) {
      setIsShowAllStore(true);
      setStoreName("All");
    } else {
      getStoreData(setStoreName, setStoreList);
    }
  }, []);

  useEffect(() => {
    if (
      startTimes !== "" &&
      endTimes !== "" &&
      startValueTime !== "" &&
      endValueTime !== "" &&
      isInitialized === true &&
      storeName !== ""
    ) {
      loadData();
    }
  }, [isInitialized]);

  /**
   * 保证切换 2 4 6区间范围时自动调接口
   */
  useEffect(() => {
    if (isInitialized === true) {
      loadData();
    }
  }, [selectedTime]);

  const loadData = () => {
    let params = {
      type: timeType,
      departmentId:
        storeName == "All" ? "0" : storeName?.id == "All" ? "0" : storeName?.id,
      startDate: startTimes.replace(/\//g, "-"),
      endDate: endTimes.replace(/\//g, "-"),
      previousStartDate: startValueTime.replace(/\//g, "-"),
      previousEndDate: endValueTime.replace(/\//g, "-"),
      hourType: selectedTime,
    };
    getDemographic(params).then((res) => {
      let data = res?.data;

      setTotalNumber(data?.totalVisitors || 0);
      setHighestDemographic(data?.highestVisitorsByDemographic);

      setHighestDemographicCount(data?.highestVisitorsByDemographicCount);
      setHighestTime(data?.highestVisitorsByTime);
      setHighestTimeCount(data?.highestVisitorsByTimeCount);

      setLeastDemographic(data?.leastVisitorsByDemographic);
      setLeastDemographicCount(data?.leastVisitorsByDemographicCount);
      setLeastTime(data?.leastVisitorsByTime);
      setLeastTimeCount(data?.leastVisitorsByTimeCount);

      setVisitorDemographic(data?.visitorDemographic || []);
      setGenderData(data?.genderDistribution || []);
      // setAgeData(data?.ageDistribution.chartDatas || []);

      if (data?.ageDistribution) {
        setAgeData(data.ageDistribution.chartDatas || []);
      } else {
        setAgeData([]);
      }
    });
  };

  const box1Ref = useRef(null);
  const box2Ref = useRef(null);

  const handleScroll = (e) => {
    if (e.target === box1Ref.current) {
      // 如果是第一个盒子的滚动，应用到第二个盒子上
      box2Ref.current.scrollTop = e.target.scrollTop;
    } else if (e.target === box2Ref.current) {
      // 如果是第二个盒子的滚动，应用到第一个盒子上
      box1Ref.current.scrollTop = e.target.scrollTop;
    }
  };

  /**
   * @param {contextValue} SelectTimeRange  组件参数
   */
  const contextValue = {
    startTimes,
    setStartTimes,
    endTimes,
    setEndTimes,
    startValueTime,
    setStartValueTime,
    endValueTime,
    setEndValueTime,
    setSelectTime,
    setTimeType,
    timeType,
    storeName,
    setStoreName,
    loadData,
    totalNumber,
    storeList,
    isShowAllStore,

    highestDemographic,
    highestDemographicCount,
    highestTime,
    highestTimeCount,
    leastDemographic,
    leastDemographicCount,
    leastTime,
    leastTimeCount,
    visitorDemographic,
    setSelectedTime,
    selectTime,
  };

  return (
    <VisitorDemographicContext.Provider value={contextValue}>
      <div className="w-full" ref={box1Ref} onScroll={handleScroll}>
        <div
          className="w-full"
          style={{
            marginBottom: "20px",
          }}>
          <SelectTimeRange></SelectTimeRange>
        </div>

        <GradientBox>
          <div className="w-full" ref={box2Ref} onScroll={handleScroll}>
            <div
              className="w-full"
              style={{
                marginTop: "30px",
                paddingLeft: "20px",
                paddingRight: "20px",
              }}>
              <FourSquares></FourSquares>
            </div>

            <div
              className="w-full"
              style={{
                marginTop: "10px",
              }}>
              <DataAnaysis></DataAnaysis>
            </div>
          </div>
        </GradientBox>

        <GradientBox>
          <div
            className="w-full"
            ref={box2Ref}
            onScroll={handleScroll}
            style={{
              margin: "10px",
            }}>
            <div className="text-base font-bold">{t("PCS57")}</div>

            <div className="flex justify-between" style={headerStyle}>
              <div
                className="bg-amber-50 rounded-2xl"
                style={{
                  margin: "8px",
                }}>
                <div
                  className="font-bold text-2xl"
                  style={{
                    margin: "12px",
                  }}>
                  {t("PCS55")}
                </div>
                <LineCharts genderData={genderData}></LineCharts>
              </div>

              <div
                style={{
                  margin: "8px",
                }}
                className="bg-amber-50 rounded-2xl">
                <div
                  className="font-bold text-2xl"
                  style={{
                    margin: "14px",
                  }}>
                  {t("PCS56")}
                </div>
                <AgeLineCharts ageData={ageData}></AgeLineCharts>
              </div>
            </div>
          </div>
        </GradientBox>
      </div>
    </VisitorDemographicContext.Provider>
  );
}
