import React, { useEffect, useState } from "react";

import { useTranslation } from "react-i18next";
import AppLanguage from "@/lang/AppLanguages";
import { parseNumber } from "@/util/parseNumber";
import AduitLineCaharts from "./AduitLineCaharts";
import GradientBox from "@c/GradientBox";
function Aduit(props) {
  const {
    aduitData,
    previousPeriodsData,
    kidsRatio,
    adultRatio,
    startValueTime,
    startTimes,
  } = props;
  const { t } = useTranslation();
  const [startMonth, setStartMonth] = useState("");
  const [endMonth, setEndMonth] = useState("");

  useEffect(() => {
    const compareTime = new Date(startValueTime);
    const times = new Date(startTimes);

    const language = AppLanguage.getPrevLanguage();
    let startMonth;
    let endMonth;
    if ("zh" === language) {
      startMonth = compareTime.toLocaleString("zh-CN", {
        month: "short",
      });
      endMonth = times.toLocaleString("zh-CN", { month: "short" });
    } else {
      startMonth = compareTime
        .toLocaleString("en-US", {
          month: "short",
        })
        .toUpperCase();
      endMonth = times
        .toLocaleString("en-US", { month: "short" })
        .toUpperCase();
    }
    setStartMonth(startMonth);
    setEndMonth(endMonth);
  }, [startTimes, startValueTime]);

  return (
    <GradientBox>
      <div
        style={{
          padding: "20px",
        }}>
        <h2 className="text-xl font-bold">{t("PCS135")}</h2>
        <h3 className="text-xl mt-2">
          {startMonth} - {endMonth}
        </h3>

        <div className="flex flex-col md:flex-row ">
          <div className="col-span-24 md:col-span-6 lg:col-span-5 xl:col-span-4 flex flex-col gap-3">
            <div className="bg-amber-50 rounded-xl shadow-lg p-4 flex flex-col items-center justify-center">
              <div
                className="text-xl "
                style={{
                  paddingLeft: "60px",
                  paddingRight: "60px",
                  whiteSpace: "nowrap",
                }}>
                {t("PCS137")}
              </div>

              <div
                style={{
                  paddingLeft: "60px",
                  paddingRight: "60px",
                }}
                className="text-white text-3xl font-bold rounded-lg bg-gradient-to-r from-[#37c1e0] to-[#21a5d5] w-full p-4 flex items-center justify-center">
                {adultRatio}%
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col items-center justify-center">
              <div
                style={{
                  paddingLeft: "60px",
                  paddingRight: "60px",
                  whiteSpace: "nowrap",
                }}
                className="text-xl mb-2">
                {t("PCS138")}
              </div>

              <div
                style={{
                  paddingLeft: "60px",
                  paddingRight: "60px",
                }}
                className="text-white rounded-lg bg-gradient-to-r from-[#9c97fc] to-[#6d65c6] text-2xl w-full p-4 flex items-center justify-center">
                {parseNumber(kidsRatio) + "%"}
              </div>
            </div>
          </div>

          <div className="w-full">
            <AduitLineCaharts
              aduitData={aduitData}
              previousPeriodsData={previousPeriodsData}
            />
          </div>
        </div>
      </div>
    </GradientBox>
  );
}

export default Aduit;
