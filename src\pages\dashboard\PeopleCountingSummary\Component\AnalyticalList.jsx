import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Grid } from "@mui/material";
import GradientBox from "@/components/GradientBox/index.jsx";
function AnalyticalList({ conversionRateList }) {
  const { t } = useTranslation();

  const cellStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
  };

  const columns = [
    {
      field: "period",
      headerName: t("PCS21"),
    },
    {
      field: "passby",
      headerName: t("PCS139"),
    },
    {
      field: "enter",
      headerName: t("PCS140"),
    },
    {
      field: "leave",
      headerName: t("PCS141"),
    },

    {
      field: "prev",
      headerName: t("PCS142"),
    },
    {
      field: "change",
      headerName: t("PCS143"),
    },
  ];

  return (
    <React.Fragment>
      <GradientBox
        style={{
          padding: "5px",
          minHeight: "30px",
          lineHeight: "30px",
          border: "1px solid #ccc",
        }}>
        <Grid columns={{ xs: 6, sm: 6, md: 6 }} container>
          {columns.map((item, index) => {
            return (
              <Grid style={cellStyle} item xs={1}>
                <span>{item?.headerName}</span>
              </Grid>
            );
          })}
        </Grid>
      </GradientBox>

      <Grid
        style={{
          maxHeight: "500px",
          overflow: "auto",
          border: "1px solid #ccc",
          marginTop: "15px",
          borderRadius: "12px",
        }}>
        {conversionRateList?.map((row, index) => {
          const change = row.change;
          const changeText = `${change > 0 ? "+" : ""}${change}%`;
          const changeColor = change < 0 ? "red" : "green";

          return (
            <GradientBox
              style={{
                padding: "5px",
                minHeight: "40px",
                lineHeight: "40px",
              }}>
              <Grid columns={{ xs: 6, sm: 6, md: 6 }} container>
                <Grid style={cellStyle} item xs={1}>
                  {row.event_date.replace(/-/g, "/")}
                </Grid>
                <Grid style={cellStyle} item xs={1}>
                  {row.passByNum}
                </Grid>
                <Grid style={cellStyle} item xs={1}>
                  {row?.inNum}
                </Grid>
                <Grid style={cellStyle} item xs={1}>
                  {row?.outNum}
                </Grid>
                <Grid style={cellStyle} item xs={1}>
                  {row.c_conversion_rate}%
                </Grid>
                <Grid style={cellStyle} item xs={1} sx={{ color: changeColor }}>
                  {row.p_conversion_rate}%
                </Grid>
              </Grid>
            </GradientBox>
          );
        })}
      </Grid>
    </React.Fragment>
  );
}

export default AnalyticalList;
