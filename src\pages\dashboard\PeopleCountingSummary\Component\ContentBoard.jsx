import React, { useEffect, useState } from "react";
import BarCharts from "./BarChart";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import { useTranslation } from "react-i18next";
import AppLanguage from "@/lang/AppLanguages";
import { parseNumber } from "@/util/parseNumber";
import { Grid } from "@mui/material";
import GradientBox from "@c/GradientBox";
function ContentBoard(props) {
  const {
    highEst,
    highEstData,
    overAll,
    barChartsData,
    previousPeriodsData,
    startValueTime,
    startTimes,
    storeName,
    animationValue,
    setAnimationValue,
  } = props;
  const [startMonth, setStartMonth] = useState("");
  const [endMonth, setEndMonth] = useState("");
  const { t } = useTranslation();
  useEffect(() => {
    const compareTime = new Date(startValueTime);
    const times = new Date(startTimes);

    const language = AppLanguage.getPrevLanguage();
    let startMonth;
    let endMonth;
    if ("zh" === language) {
      startMonth = compareTime.toLocaleString("zh-CN", {
        month: "short",
      });
      endMonth = times.toLocaleString("zh-CN", { month: "short" });
    } else {
      startMonth = compareTime
        .toLocaleString("en-US", {
          month: "short",
        })
        .toUpperCase();
      endMonth = times
        .toLocaleString("en-US", { month: "short" })
        .toUpperCase();
    }
    setStartMonth(startMonth);
    setEndMonth(endMonth);
  }, [startTimes, startValueTime]);

  return (
    <GradientBox>
      <Grid m={2}>
        <h2 className="text-xl font-semibold">{t("PCS12")}</h2>

        <div className="flex flex-col md:flex-row gap-2">
          <div className="flex flex-col gap-2">
            <div
              className="bg-white rounded-xl gap-3 text-xl flex flex-col items-center justify-center whitespace-nowrap text-center shadow-lg  w-55 h-40"
              style={{
                boxShadow: "2px 4px 6px rgba(0, 0, 0, 0.4)",
              }}>
              <div className="text-xl p-2">{t("PCS13")}</div>
              <div className="font-bold text-xl">
                {startMonth} - {endMonth}
              </div>

              <div
                className={`rounded-[8px] w-40 h-22 text-3xl mx-auto  flex flex-row items-center justify-center ${
                  overAll < 0
                    ? "bg-gradient-to-t from-[#fccb90] to-[#d57eeb]"
                    : "bg-gradient-to-r from-[#37ecba] to-[#72afd3]"
                }`}
                style={{
                  marginLeft: "20px",
                  marginRight: "20px",
                  marginBottom: "10px",
                }}>
                {overAll < 0 ? <ArrowDownwardIcon /> : <ArrowUpwardIcon />}
                {overAll}%
              </div>
            </div>

            <div
              className="bg-white rounded-xl gap-3 text-xl flex flex-col items-center justify-center whitespace-nowrap text-center shadow-lg  w-55 h-40"
              style={{
                boxShadow: "2px 4px 6px rgba(0, 0, 0, 0.4)",
              }}>
              <div>{t("PCS14")}</div>
              <div className="font-bold">{highEstData}</div>
              <div
                className="bg-gradient-to-r flex flex-col items-center justify-center from-[#4facfe] to-[#00f2fe] w-40 h-22 text-white rounded-lg"
                style={{
                  marginLeft: "10px",
                  marginRight: "10px",
                  marginBottom: "10px",
                }}>
                <div className="text-3xl">{parseNumber(highEst)}</div>
                <div className="text-base">{t("PCS31")}</div>
              </div>
            </div>
          </div>

          <div className="flex-1 min-w-[60%]">
            <BarCharts
              barChartsData={barChartsData}
              previousPeriodsData={previousPeriodsData}
              storeName={storeName}
              animationValue={animationValue}
              setAnimationValue={setAnimationValue}
            />
          </div>
        </div>
      </Grid>
    </GradientBox>
  );
}

export default ContentBoard;
