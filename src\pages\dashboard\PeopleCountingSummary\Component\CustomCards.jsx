import React, { useEffect, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { Box, Grid, Typography, Tooltip } from "@mui/material";
import { useTranslation } from "react-i18next";
import GradientBox from "@/components/GradientBox";

function CustomCards({
  title,
  startTimes,
  endTimes,
  onClick,
  open,
  startName,
  endName,
  isShowAllStore,
  storeName,
  store,
  locationName,
}) {
  const { t } = useTranslation();

  return (
    <GradientBox onClick={onClick}>
      <div
        className="w-full max-w-[400px] min-w-[220px] rounded-2xl p-2.5 sm:h-[60px] md:h-[80px] lg:h-[100px]
            sm:max-w-[300px] sm:p-3
            md:max-w-[350px] md:p-4
            lg:max-w-[400px] lg:p-5">
        <div>
          <div className="text-xs sm:text-3xl md:text-2xl lg:text-sm  text-[rgba(121,121,121,1)]">
            {title}
          </div>

          <Grid
            display={"flex"}
            alignItems="center"
            justifyContent="space-between"
            mt={2}>
            {store ? (
              <Tooltip
                title={
                  storeName == "All" && isShowAllStore
                    ? t("PCS09")
                    : storeName?.name
                    ? storeName?.name
                    : ""
                }
                arrow
                placement="bottom"
                className="text-xs sm:text-3xl md:text-2xl lg:text-sm  font-bold text-[rgba(14,14,14,1)]">
                {storeName == "All" && isShowAllStore
                  ? t("Select Outlets")
                  : storeName?.name
                  ? storeName?.name
                  : ""}
              </Tooltip>
            ) : (
              <div className="text-xs sm:text-3xl md:text-2xl lg:text-sm   font-bold text-[rgba(14,14,14,1)] whitespace-nowrap">
                {startTimes || startName} ~ {endTimes || endName}
              </div>
            )}

            {locationName && (
              <div className="text-xs sm:text-3xl md:text-lg lg:text-sm  font-bold text-[rgba(14,14,14,1)] whitespace-nowrap">
                {locationName}
              </div>
            )}

            <div style={{ marginLeft: "10px" }}>
              {open ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </div>
          </Grid>
        </div>
      </div>
    </GradientBox>
  );
}

export default CustomCards;
