/* eslint-disable no-undef */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState } from "react";
import { Button, Grid, Box, Typography } from "@mui/material";
import CustomCards from "./CustomCards";
import DialogTime from "./DialogTime";
import CompareTime from "./CompareTime";
import { useTranslation } from "react-i18next";
import { parseNumber } from "@/util/parseNumber";
import GradientBox from "@/components/GradientBox";
import SelectOutlet from "../../Component/SelectOutlet";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { exportPeopleCounting } from "@/services/dashboard.js";
import { format } from "date-fns";
import AuthButton from "@/components/AuthButton.jsx";
function HeardBoard(props) {
  const {
    startTimes,
    setStartTimes,
    endTimes,
    setEndTimes,
    startValueTime,
    setStartValueTime,
    endValueTime,
    setEndValueTime,
    selectTime,
    setSelectTime,
    loadData,
    storeName,
    setStoreName,
    storeList,
    totalVistord,
    isShowAllStore,
    countryInfo,
    stateInfo,
    cityInfo,
  } = props;
  const { t } = useTranslation();
  const totalValue = parseNumber(totalVistord);
  const [open, setOpen] = React.useState(false); //打开第一个时间弹窗
  const [open1, setOpen1] = useState(false);
  const [storeOpen, setStoreOpen] = useState(false); //所有门店下拉弹窗
  const [resetPage, setResetPage] = useState(false);

  useEffect(() => {
    setStoreName("All");
  }, [cityInfo]);

  //导出数据方法
  const handlerExport = () => {
    let params = {
      type: selectTime, //按月导出 / 按天导出
      departmentId: storeName?.id,
      startDate: startTimes.replace(/\//g, "-"),
      endDate: endTimes.replace(/\//g, "-"),
      previousStartDate: startValueTime.replace(/\//g, "-"),
      previousEndDate: endValueTime.replace(/\//g, "-"),
      country: countryInfo?.id,
      state: stateInfo?.id,
      city: cityInfo?.id,
      countryName: countryInfo?.name,
      stateName: stateInfo?.name,
      cityName: cityInfo?.name,
    };

    exportPeopleCounting(params).then((res) => {
      const link = document.createElement("a");
      let blob = new Blob([res], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
      });
      link.style.display = "none";
      // link.download = res.headers[""]; //下载后文件名
      link.download =
        "PeopCounting_Data_" + format(new Date(), "yyyy_MM_dd_HH_mm_ss"); //下载的文件名
      link.href = URL.createObjectURL(blob);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  };

  return (
    <React.Fragment>
      <Grid container xs={12} m={2}>
        <Grid container xs={10} spacing={2}>
          <GradientBox>
            <Box
              sx={{
                p: 2,
              }}
              onClick={() => {
                setCurOpen(true);
              }}>
              <Typography
                style={{
                  fontSize: "14px",
                  color: "rgba(121,121,121,1)",
                }}>
                {t("PCS03")}
              </Typography>
              <Grid
                sx={{
                  display: "flex",
                  mt: 2,
                }}>
                <Typography
                  style={{
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "rgba(14,14,14,1)",
                    whiteSpace: "nowrap",
                  }}>
                  {startTimes || t("PCS05")} - {endTimes || t("PCS06")}
                </Typography>

                <Box ml={2}>
                  {open ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </Box>
              </Grid>
            </Box>
          </GradientBox>

          <GradientBox
            style={{
              marginLeft: "15px",
            }}>
            <Grid
              item
              xs={4}
              sx={{
                p: 2,
              }}
              onClick={() => {
                setPerOpen(true);
              }}>
              <Typography
                style={{
                  fontSize: "14px",
                  color: "rgba(121,121,121,1)",
                }}>
                {t("PCS03")}
              </Typography>
              <Grid
                sx={{
                  display: "flex",
                  mt: 2,
                }}>
                <Typography
                  style={{
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "rgba(14,14,14,1)",
                    whiteSpace: "nowrap",
                  }}>
                  {startValueTime || t("PCS05")} - {endValueTime || t("PCS06")}
                </Typography>

                <Box ml={2}>
                  {open1 ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </Box>
              </Grid>
            </Grid>
          </GradientBox>

          <GradientBox
            style={{
              marginLeft: "15px",
            }}>
            <Grid
              item
              xs={4}
              sx={{
                p: 2,
              }}
              onClick={() => {
                setStoreOpen(true);
              }}>
              <Typography
                style={{
                  fontSize: "14px",
                  color: "rgba(121,121,121,1)",
                  width: "200px",
                }}>
                {t("PCS08")}
              </Typography>
              <Grid
                sx={{
                  display: "flex",
                  mt: 2,
                }}>
                <Typography
                  style={{
                    fontSize: "14px",
                    fontWeight: "700",
                    color: "rgba(14,14,14,1)",
                    whiteSpace: "nowrap",
                  }}>
                  {storeName == "All" ? t("PCS09") : storeName?.name}
                </Typography>

                <Box ml={2}>
                  {storeOpen ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </Box>
              </Grid>
            </Grid>
          </GradientBox>

          <GradientBox
            style={{
              marginLeft: "15px",
            }}>
            <Button
              variant="contained"
              onClick={() => loadData()}
              sx={{
                borderRadius: "8px",
                fontWeight: 700,
                fontSize: "18px",
                width: "100%",
                height: "100%",
              }}>
              <span dangerouslySetInnerHTML={{ __html: t("PCS10") }}></span>
            </Button>
          </GradientBox>
        </Grid>

        <Grid container xs={2} justifyContent="end">
          <Grid
            item
            xs={2}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "end",
              fontSize: "22px",
              fontWight: 700,
              color: "#878787",
            }}>
            <span dangerouslySetInnerHTML={{ __html: t("PCS64") }} />
          </Grid>
          <Grid
            item
            xs={8}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: "#fff",
              textAlign: "center",
              borderRadius: "15px",
              fontSize: "28px",
              fontWeight: 700,
              marginLeft: "10px",
            }}>
            <span
              style={{
                background: `linear-gradient(to right, #71b652, #3599d7)`,
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}>
              {parseNumber(totalVistord)}
            </span>
          </Grid>
        </Grid>
      </Grid>

      {/* 第一个时间选择组件 */}
      <DialogTime
        open={open}
        setOpen={setOpen}
        setStartTimes={setStartTimes}
        endTimes={endTimes}
        setEndTimes={setEndTimes}
        setStartValueTime={setStartValueTime}
        setEndValueTime={setEndValueTime}
        selectTime={selectTime}
        setSelectTime={setSelectTime}></DialogTime>
      {/* 第二个时间范围选择组件 */}
      <CompareTime
        open1={open1}
        endTimes={endTimes}
        setOpen1={setOpen1}
        startTimes={startTimes}
        selectTime={selectTime}
        setStartValueTime={setStartValueTime}
        setEndValueTime={setEndValueTime}></CompareTime>
      {storeName && (
        <SelectOutlet
          storeOpen={storeOpen}
          setStoreOpen={setStoreOpen}
          setStoreName={setStoreName}
          storeName={storeName}
          storeList={storeList}
          isShowAllStore={isShowAllStore}
          countryInfo={countryInfo}
          stateInfo={stateInfo}
          cityInfo={cityInfo}
          resetPage={resetPage}
          setResetPage={setResetPage}></SelectOutlet>
      )}
    </React.Fragment>
  );
}
export default HeardBoard;
