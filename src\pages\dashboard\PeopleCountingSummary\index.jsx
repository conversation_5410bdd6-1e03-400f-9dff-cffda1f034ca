import React, { useEffect, useState } from "react";
/* eslint-disable no-undef */
/* eslint-disable react-hooks/rules-of-hooks */
import { useStateUserInfo } from "@/hooks/user";
import { Box, Grid, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";

import { useTranslation } from "react-i18next";

import AnalyticalList from "./Component/AnalyticalList";
import ContentBoard from "./Component/ContentBoard";
import { getStoreData, getTimeData } from "./Component/GetTime";
import HeaderBoard from "./Component/HeaderBoard";
import { getPeopleCounting } from "@/services/dashboard.js";
import LineCharts from "./Component/LineCharts";
import Aduit from "./Component/Aduit";
import GradientBox from "@c/GradientBox";
import { toast } from "react-toastify";
function index() {
  const { t } = useTranslation();

  const theme = useTheme();

  const [startTimes, setStartTimes] = useState(""); //第一个时间组件选中的开始时间
  const [endTimes, setEndTimes] = useState(""); //第一个时间组件选中的结束时间
  const [startValueTime, setStartValueTime] = useState(""); //第二个时间组件选中的开始时间
  const [endValueTime, setEndValueTime] = useState(""); //第二个时间组件选中的结束时间

  const [totalVistord, setTotalVistord] = useState(0);
  const [highEst, setHighEst] = useState(0);
  const [highEstData, setHighEstData] = useState("");
  const [overAll, setOverAll] = useState(0);
  const [storeList, setStoreList] = useState([]); //所有门店数据
  const [storeName, setStoreName] = useState(""); //选中的门店名称
  const [barChartsData, setBarChartsData] = useState([]); // 柱状图列表数据
  const [previousPeriodsData, setPreviousPeriodsData] = useState([]);
  const [selectTime, setSelectTime] = useState(2); //选择的时间范围 类型 1 为7天 2 为5天
  const [conversionRateList, setConversionRateList] = useState([]);
  const [animationValue, setAnimationValue] = useState(true); //处理切换门店时禁止动画渲染
  const [aduitData, setAduitData] = useState([]);
  const [kidsRatio, setKidsRatio] = useState(0);
  const [adultRatio, setAdultRatio] = useState(0);

  const [countryInfo, setCountryInfo] = useState({});
  const [stateInfo, setStateInfo] = useState({});
  const [cityInfo, setCityInfo] = useState({});

  //主要判断是否初始化  初始化完成后，只能点击应用按钮才能调接口
  const [isInitialize, setIsInitialize] = useState(false);
  // 获取当前用户类型

  const [isShowAllStore, setIsShowAllStore] = useState(false);

  //设置当前时间和比较时间方法
  useEffect(() => {
    getTimeData(setStartTimes, setEndTimes, setStartValueTime, setEndValueTime);
    if (isInitialize === true) return;
    setIsInitialize(true);
  }, [isInitialize]);

  const roles = JSON.parse(sessionStorage.getItem("USER_INFO"));

  useEffect(() => {
    if (roles && roles?.employeeType !== 2) {
      setIsShowAllStore(true);
      setStoreName("All");
    } else {
      getStoreData(setStoreName, setStoreList);
    }
  }, []);

  useEffect(() => {
    if (
      startTimes !== "" &&
      endTimes !== "" &&
      startValueTime !== "" &&
      endValueTime !== "" &&
      isInitialize === true &&
      storeName !== ""
    ) {
      loadData();
    }
  }, [isInitialize]);

  const loadData = () => {
    let params = {
      type: selectTime,
      departmentId:
        storeName == "All" ? "0" : storeName?.id == "All" ? "0" : storeName?.id,
      startDate: startTimes.replace(/\//g, "-"),
      endDate: endTimes.replace(/\//g, "-"),
      previousStartDate: startValueTime.replace(/\//g, "-"),
      previousEndDate: endValueTime.replace(/\//g, "-"),
    };

    getPeopleCounting(params).then((res) => {
      if (res?.code == "00000000") {
        let data = res?.data;
        setTotalVistord(data?.totalVistord);
        setHighEst(data?.highestEntry);
        setHighEstData(data?.highestEntryDate);
        setOverAll(data?.overallChange);
        setBarChartsData(data?.entryVolume);
        setPreviousPeriodsData(data?.previousPeriod);
        setConversionRateList(data?.conversionRateList);
        setAduitData(data?.entryAdultAndKidsList);
        setKidsRatio(data?.kidsRatio);
        setAdultRatio(data?.adultRatio);
      }
    });
  };
  return (
    <Grid container>
      {/* <div className="w-full">
        <Box sx={{ fontWeight: 700, color: "#000", fontSize: "24px" }}>
          {t("PCS02")}
        </Box>
        <Box fontSize={28}>{t("PCS01")}</Box>
      </div> */}

      <div
        className="w-full"
        style={{
          margin: "10px",
        }}>
        <HeaderBoard
          startTimes={startTimes}
          setStartTimes={setStartTimes}
          endTimes={endTimes}
          setEndTimes={setEndTimes}
          startValueTime={startValueTime}
          setStartValueTime={setStartValueTime}
          endValueTime={endValueTime}
          setEndValueTime={setEndValueTime}
          selectTime={selectTime}
          loadData={loadData}
          setSelectTime={setSelectTime}
          totalVistord={totalVistord}
          storeName={storeName}
          setStoreName={setStoreName}
          storeList={storeList}
          isShowAllStore={isShowAllStore}
          countryInfo={countryInfo}
          setCountryInfo={setCountryInfo}
          stateInfo={stateInfo}
          setStateInfo={setStateInfo}
          cityInfo={cityInfo}
          setCityInfo={setCityInfo}></HeaderBoard>
      </div>

      <div
        className="w-full"
        style={{
          margin: "10px",
        }}>
        <ContentBoard
          highEst={highEst}
          highEstData={highEstData}
          overAll={overAll}
          barChartsData={barChartsData}
          previousPeriodsData={previousPeriodsData}
          startTimes={startTimes}
          startValueTime={startValueTime}
          storeName={storeName}
          setAnimationValue={setAnimationValue}
          animationValue={animationValue}></ContentBoard>
      </div>

      <div
        className="w-full"
        style={{
          margin: "10px",
        }}>
        <GradientBox>
          <div
            className="text-xl font-semibold"
            style={{
              margin: "20px",
            }}>
            {t("PCS18")}
          </div>

          <Grid container xs={12} spacing={2}>
            <Grid item xs={6} md={6} lg={6}>
              <LineCharts
                previousPeriodsData={previousPeriodsData}
                conversionRateList={conversionRateList}
                storeName={storeName}
                setAnimationValue={setAnimationValue}
                animationValue={animationValue}
              />
            </Grid>

            <Grid item xs={6} md={6} lg={6}>
              <AnalyticalList conversionRateList={conversionRateList} />
            </Grid>
          </Grid>
        </GradientBox>
      </div>

      <div
        className="w-full"
        style={{
          margin: "10px",
        }}>
        <Aduit
          startTimes={startTimes}
          startValueTime={startValueTime}
          aduitData={aduitData}
          kidsRatio={kidsRatio}
          adultRatio={adultRatio}
          previousPeriodsData={previousPeriodsData}></Aduit>
      </div>
    </Grid>
  );
}

export default index;
