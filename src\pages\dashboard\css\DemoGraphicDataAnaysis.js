export const buttonStyles = {
  height: "60px",
  borderRadius: "35px",
  fontWeight: 700,
  position: "relative",
};

export const timeBoxStyle = {
  background: "#fff",
  borderRadius: "10px",
  overflowY: "auto",
  border: "1px solid #fff",
  boxShadow: `6px 6px 12px #c5c5c5, -6px -6px 12px #ffffff`,
  marginTop: "15px",
  scrollbarWidth: "thin",
  scrollbarColor: "#d1e7c3 transparent",
};

timeBoxStyle["&::-webkit-scrollbar"] = {
  width: "4px",
};

timeBoxStyle["&::-webkit-scrollbar-thumb"] = {
  background: "#d1e7c3",
  borderRadius: "4px",
};

timeBoxStyle["&::-webkit-scrollbar-track"] = {
  background: "transparent",
};

export const outerBoxStyle = {
  ":hover": {
    background: "#61bc84", // 修改背景颜色
    boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.3)", // 增加阴影效果
    color: "#FFF",
    fontWeight: 700,
    borderRadius: "20px",
  },

  ":active": {
    background: "#61bc84", // 修改背景颜色
    transform: "scale(0.95)", // 缩放效果
  },
};

export const getBgStyle = (val) => {
  if (val < 100) {
    return {
      backgroundColor: "rgb(232, 243, 226)",
    };
  }
  if (val < 300) {
    return {
      backgroundColor: "rgb(209, 231, 195)",
    };
  }

  return {
    backgroundColor: "rgb(232, 243, 226)",
  };
};

export const tbodyStyle = {
  marginTop: "20px",
  background: "#FFF",
  boxShadow: "1px 2px 4px rgba(0, 0, 0, 0.4)",
  borderRadius: "10px",
  overflowY: "auto",
  overflowX: "hidden",
  maxHeight: "30vh",
  scrollbarWidth: "thin",
  scrollbarColor: "#d1e7c3 transparent",
};

tbodyStyle["&::-webkit-scrollbar"] = {
  width: "4px",
};

tbodyStyle["&::-webkit-scrollbar-thumb"] = {
  background: "#d1e7c3",
  borderRadius: "4px",
};

tbodyStyle["&::-webkit-scrollbar-track"] = {
  background: "transparent",
};

export const devicesMateDataStyle = {
  maxHeight: "590px",
  overflowY: "auto",
  scrollbarWidth: "thin",
  scrollbarColor: "#f4f7f9 transparent",
};

devicesMateDataStyle["&::-webkit-scrollbar"] = {
  width: "4px",
};

devicesMateDataStyle["&::-webkit-scrollbar-thumb"] = {
  background: "#f4f7f9",
  borderRadius: "4px",
};

devicesMateDataStyle["&::-webkit-scrollbar-track"] = {
  background: "transparent",
};

export const totalStyle = {
  width: {
    xs: "4.2vw",
    sm: "4.8vw",
    md: "6vw",
    lg: "6.3vw",
  },
  // overflowX: "auto",
  // scrollbarWidth: "thin",
  // scrollbarColor: "#f4f7f9 transparent",
};

totalStyle["&::-webkit-scrollbar"] = {
  width: "1px",
};

totalStyle["&::-webkit-scrollbar-thumb"] = {
  background: "#f4f7f9",
  borderRadius: "4px",
};

totalStyle["&::-webkit-scrollbar-track"] = {
  background: "transparent",
};
