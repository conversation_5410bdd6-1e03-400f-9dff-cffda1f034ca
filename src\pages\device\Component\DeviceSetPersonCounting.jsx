/* eslint-disable no-restricted-globals */
/* eslint-disable no-undef */
import React, { useEffect, useRef, useState } from "react";
import RightViewLayout from "../../../components/RighViewLayout";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormControl from "@mui/material/FormControl";
import FormLabel from "@mui/material/FormLabel";
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  Typography,
  Autocomplete,
  TextField,
} from "@mui/material";
import { REACT_DEVICE_LIST } from "@/router/ReactEndPoints";
import { useNavigate } from "react-router-dom";
import DeviceIcon from "@/assets/images/icon_device.png";
import { t } from "i18next";
import { useTranslation } from "react-i18next";
export default function DeviceSetPersonCounting() {
  let navigate = useNavigate();
  useEffect(() => {
    //初始化画布
    canvasInit("#channel_canvas");
    //初始化比例
    scanInit();
  });
  //-----------------画线操作------------------------
  //是否可编辑
  let editFlag = useRef(false);
  //画线类型
  let lineType = useRef(1);
  //方向类型：1：单向，2：双向
  let arrows = useRef(1);
  //描点的数量
  let pointSize = useRef(2);
  //半径
  let radius = useRef(4);
  //图片与画布比例，默认为1
  let widthScan = useRef(1);
  let heightScan = useRef(1);
  let canvas = useRef(null);
  let ctx = useRef(null);
  let pointMapArray = new Map();
  let type1 = new Map();
  let type2 = new Map();
  let type3 = new Map();
  const channelWrapRef = useRef(null);
  const channelContainerRef = useRef(null);

  const canvasInit = (canvasSelector) => {
    canvas = document.querySelector(canvasSelector);
    canvas.width = channelWrapRef.current.getBoundingClientRect().width;
    canvas.height = channelWrapRef.current.getBoundingClientRect().height;
    ctx = canvas.getContext("2d");
    ctx.lineWidth = 3;
    ctx.strokeStyle = "#f00";
  };
  const scanInit = () => {
    let image = document.getElementById("snapImage");
    image.onload = function () {
      widthScan = image.width / canvas.width;
      heightScan = image.height / canvas.height;
    };
  };
  //清空
  const clearCtx = () => {
    pointMapArray = new Map();
    channelContainerRef.current.innerHTML = "";
    editFlag = true;
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
  };
  //添加点位移动事件监听
  const addDragListener = (pointId) => {
    let obj = document.getElementById(pointId);
    let dmW = document.documentElement.clientWidth || document.body.clientWidth;
    let dmH =
      document.documentElement.clientHeight || document.body.clientHeight;
    let sent = {};
    let l = sent.l || 0;
    let r = sent.r || dmW - obj.offsetWidth;
    let t = sent.t || 0;
    let b = sent.b || dmH - obj.offsetHeight;
    obj.onmousedown = function (ev) {
      let oEvent = ev || event;
      let sentX = oEvent.clientX - obj.offsetLeft;
      let sentY = oEvent.clientY - obj.offsetTop;
      document.onmousemove = function (ev) {
        let oEvent = ev || event;
        let slideLeft = oEvent.clientX - sentX;
        let slideTop = oEvent.clientY - sentY;

        if (slideLeft <= l) {
          slideLeft = l;
        }
        if (slideLeft >= r) {
          slideLeft = r;
        }
        if (slideTop <= t) {
          slideTop = t;
        }
        if (slideTop >= b) {
          slideTop = b;
        }
        obj.style.left = slideLeft - radius.current + "px";
        obj.style.top = slideTop - radius.current + "px";
        pointMapArray
          .get("type" + lineType.current)
          .set(pointId, { x: slideLeft, y: slideTop });
        drawPolygon(pointMapArray.get("type" + lineType.current));
      };
      document.onmouseup = function () {
        document.onmousemove = null;
        document.onmouseup = null;
      };
      return false;
    };
  };
  //箭头
  const drawLineArrow = (ctx, fromX, fromY, toX, toY, color) => {
    //ctx为需要绘制的canvas元素的CanvasRenderingContext2D 对象，使用它可以绘制到 Canvas 元素中。
    var headlen = 10; // 自定义箭头线的长度
    var theta = 45; // 自定义箭头线与直线的夹角
    var arrowX, arrowY; // 箭头线终点坐标
    // 计算各角度和对应的箭头终点坐标
    var angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI;
    var angle1 = ((angle + theta) * Math.PI) / 180;
    var angle2 = ((angle - theta) * Math.PI) / 180;
    var topX = headlen * Math.cos(angle1);
    var topY = headlen * Math.sin(angle1);
    var botX = headlen * Math.cos(angle2);
    var botY = headlen * Math.sin(angle2);
    ctx.beginPath();
    // 画直线
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);

    arrowX = toX + topX;
    arrowY = toY + topY;
    // 画上边箭头线
    ctx.moveTo(arrowX, arrowY);
    ctx.lineTo(toX, toY);

    arrowX = toX + botX;
    arrowY = toY + botY;
    // 画下边箭头线
    ctx.lineTo(arrowX, arrowY);
    ctx.strokeStyle = color;
    ctx.stroke();
  };

  //定点
  const getArrowPoint = (ctx, pointMap) => {
    if (pointMap.size === 2) {
      // 第1个点的X
      let x1 = pointMap.get("point_0").x;
      // 第2个点的X
      let x2 = pointMap.get("point_1").x;
      // 第1个点的y
      let y1 = pointMap.get("point_0").y;
      // 第2个点的y
      let y2 = pointMap.get("point_1").y;
      // 中点c的位置为
      let c = { x: (x1 + x2) / 2, y: (y1 + y2) / 2 };

      // 根据x、y的值 计算borderX, borderY
      let borderX = Math.abs(x1 - x2);
      let borderY = Math.abs(y1 - y2);

      // 斜边l1的长度为
      let l1 = Math.sqrt(Math.pow(borderX, 2) + Math.pow(borderY, 2));

      // 定义l2的默认长度为50
      let l2 = 40;
      let h2 = (l2 * borderX) / l1;
      let s2 = (l2 * borderY) / l1;

      let endX = y1 >= y2 ? c.x - s2 : c.x + s2;
      let endY = x1 >= x2 ? c.y + h2 : c.y - h2;
      let end2X = 2 * c.x - endX;
      let end2Y = 2 * c.y - endY;
      //drawLineArrow为 画箭头的方法

      drawLineArrow(ctx, c.x, c.y, endX, endY, "#f00", "A");
      //这里控制是单向单拌线还是双向单拌线
      // if (arrows === 2){
      //     drawLineArrow(ctx, c.x, c.y, end2X, end2Y, '#f00', 'B');
      // }
    }
  };

  //点位连线
  const drawPolygon = (pointMap) => {
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
      if (pointMap && pointMap.size > 0) {
        ctx.moveTo(pointMap.get("point_0").x, pointMap.get("point_0").y);
        for (var i = 1; i < pointMap.size; i++) {
          if (lineType.current === 2 && i % 2 === 0) {
            ctx.moveTo(
              pointMap.get("point_" + i).x,
              pointMap.get("point_" + i).y
            );
          } else {
            ctx.lineTo(
              pointMap.get("point_" + i).x,
              pointMap.get("point_" + i).y
            );
          }
        }
        ctx.closePath();
        ctx.stroke();
      }
      //这里控制是拌线还是区域
      if (lineType.current === 1) {
        getArrowPoint(ctx, pointMap);
      } else if (lineType.current === 2) {
        let point_1 = new Map();
        let point_2 = new Map();
        for (var j = 0; j < pointMap.size; j++) {
          if (j > 1) {
            point_1.set("point_" + (j - 2), pointMap.get("point_" + j));
          } else {
            point_2.set("point_" + j, pointMap.get("point_" + j));
          }
        }
        getArrowPoint(ctx, point_1);
        getArrowPoint(ctx, point_2);
      }
    }
  };
  //点位样式
  const setContainerHtml = (left, top) => {
    let map;
    if (pointMapArray.get("type" + lineType.current)) {
      map = pointMapArray.get("type" + lineType.current);
    } else {
      map = new Map();
    }
    // let map = pointMapArray.get('type'+lineType.current) || new Map();
    let pointId = "point_" + map.size;
    let point = document.createElement("div");
    // point.setAttribute("class","channel_point");
    point.style.width = "10px";
    point.style.height = "10px";
    point.style.background = "red";
    point.style.borderRadius = "50%";
    point.style.position = "absolute";
    point.style.cursor = "move";
    point.setAttribute("id", pointId);
    point.style.left = left - radius.current + "px";
    point.style.top = top - radius.current + "px";
    // document.getElementById("channel_container").appendChild(newDiv);
    channelContainerRef.current.appendChild(point);
    map.set("point_" + map.size, { x: left, y: top });
    pointMapArray.set("type" + lineType.current, map);
    addDragListener(pointId);
  };

  const editPoints = (e) => {
    if (lineType.current === 1) {
      if (pointMapArray.get("type1") && pointMapArray.get("type1").size >= 2) {
        editFlag = false;
        return;
      }
      const offset = channelWrapRef.current.getBoundingClientRect();
      const left = e.pageX - offset.left;
      const top = e.pageY - offset.top;
      setContainerHtml(left, top);
      drawPolygon(pointMapArray.get("type" + lineType.current));
    } else if (lineType.current === 2) {
      if (pointMapArray.get("type2") && pointMapArray.get("type2").size >= 4) {
        editFlag = false;
        return;
      }
      const offset = channelWrapRef.current.getBoundingClientRect();
      const left = e.pageX - offset.left;
      const top = e.pageY - offset.top;
      setContainerHtml(left, top);
      drawPolygon(pointMapArray.get("type" + lineType.current));
    } else {
      if (pointMapArray.get("type3") && pointMapArray.get("type3").size >= 6) {
        editFlag = false;
        return;
      }
      const offset = channelWrapRef.current.getBoundingClientRect();
      const left = e.pageX - offset.left;
      const top = e.pageY - offset.top;
      setContainerHtml(left, top);
      drawPolygon(pointMapArray.get("type" + lineType.current));
    }
  };
  //----------------------------------------
  const [areaType, setAreaType] = React.useState("0");
  const areaTypeHandleChange = (event) => {
    setAreaType(event.target.value);
    clearCtx();
    if (event.target.value === "0") {
      lineType.current = 1;
      pointSize.current = 2;
    }
    if (event.target.value === "1") {
      lineType.current = 3;
      pointSize.current = 6;
    }
  };
  return (
    <>
      <Grid xs={12} mb={2} style={{ color: "#6a7171" }}>
        {t("DSPC001")}
      </Grid>
      <Grid xs={12} mb={4}>
        <div
          ref={channelWrapRef}
          className="channel_wrap"
          id="channel_wrap"
          style={{
            width: "100%",
            height: "800px",
            background: "#ccc",
            position: "relative",
          }}
        >
          <div
            onClick={editPoints}
            style={{
              width: "100%",
              height: "100%",
              backgroundImage: `url(${DeviceIcon})`,
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
              backgroundSize: "100% 100%",
            }}
          >
            <canvas id="channel_canvas">no support canvas</canvas>
            <img
              id="snapImage"
              src={DeviceIcon}
              style={{ width: "100%", height: "100%", display: "none" }}
            />
          </div>
          <div
            ref={channelContainerRef}
            id="channel_container"
            className="channel_container"
          ></div>
        </div>
      </Grid>
      <Grid container xs={12}>
        <Grid container alignItems="center" xs={6}>
          <Grid item mx={2}>
            <FormControl component="fieldset">
              <FormLabel component="legend">{t("DSPC002")}</FormLabel>
            </FormControl>
          </Grid>
          <Grid item>
            <RadioGroup
              row
              aria-labelledby="demo-controlled-radio-buttons-group"
              name="controlled-radio-buttons-group"
              value={areaType}
              onChange={areaTypeHandleChange}
            >
              <FormControlLabel
                value="0"
                control={<Radio />}
                label={t("DSPC003")}
              />
              <FormControlLabel
                value="1"
                control={<Radio />}
                label={t("DSPC004")}
              />
            </RadioGroup>
          </Grid>
        </Grid>
        <Grid
          item
          xs={6}
          style={{ display: "flex", justifyContent: "flex-end" }}
        >
          <Grid item>
            <Box display={"flex"} flexDirection={"row-reverse"}>
              <Box item pl={2}>
                <Button
                  variant="contained"
                  size="large"
                  className="text-transform-none"
                  // onClick={handleSubmit}
                >
                  {t("LVL0013")}
                </Button>
              </Box>
              <Box item>
                <Button
                  className="text-transform-none"
                  variant="outlined"
                  onClick={() => navigate(REACT_DEVICE_LIST)}
                  size="large"
                >
                  {t("LVLDB0012")}
                </Button>
              </Box>
              <Box item pl={2}>
                <Button className="text-transform-none" size="large">
                  {t("LVLDB0014")}
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </>
  );
}
