import React, { useState, useRef, useEffect } from "react";

const DrawCanvas = (props) => {
  let width = props.width || 300;
  let height = props.height || 300;

  let maxPointNum = props.maxPointNum || 6;

  let pointRadius = props.maxPointNum || 6;

  const container = useRef(null);

  const [ctx, setCtx] = useState(null);

  let canvasStyle = {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    cursor: "crosshair",
  };

  let [pointList, setPointList] = useState([]);

  let [tmpPoint, setTmpPoint] = useState(null);
  let [isClosePath, setIsClosePath] = useState(false);
  let [isMousedown, setIsMousedown] = useState(false);
  let [dragPointIndex, setDragPointIndex] = useState(-1);

  useEffect(() => {
    init();
  }, []);

  const init = () => {
    let canvas = container.current;
    canvas.width = width;
    canvas.height = height;
    let ctx = canvas.getContext("2d");
    setCtx(ctx);
  };

  let draw = (e) => {
    
    if (isClosePath) {
      return false;
    }
    let nativeEvent = e.nativeEvent;
    let temp = [
      ...pointList,
      {
        x: nativeEvent.offsetX,
        y: nativeEvent.offsetY,
      },
    ];
    setPointList(temp);
  };

  let render = () => {
    ctx.clearRect(0, 0, width, height);
    ctx.beginPath();
    let pointsArr = [];
    if (tmpPoint) {
      pointsArr = [...pointList, tmpPoint];
    } else {
      pointsArr = [...pointList];
    }
    pointsArr.forEach((item, index) => {
      if (index === 0) {
        ctx.moveTo(item.x, item.y);
      } else {
        ctx.lineTo(item.x, item.y);
      }
    });
    ctx.closePath();
    ctx.lineWidth = 5;
    ctx.strokeStyle = "#38a4ec";
    ctx.lineJoin = "round"; // 线段连接处圆滑一点更好看
    ctx.stroke();

    ctx.save(); // 因为要重新设置绘图样式，为了不影响线段，所以需要保存一下绘图状态
    ctx.lineWidth = 2;
    ctx.strokeStyle = "#1791fc";
    ctx.fillStyle = "#fff";
    pointList.forEach((item, index) => {
      ctx.beginPath();
      ctx.arc(item.x, item.y, pointRadius, 0, 2 * Math.PI);
      ctx.fill();
      ctx.stroke();
    });
    ctx.restore(); // 恢复绘图状态
  };

  let onDbClick = () => {
    
    setIsClosePath(true);
    setTmpPoint(null);
    setIsMousedown(false);
    render();
  };

  let mousemove = (e) => {
    if (isClosePath && isMousedown && dragPointIndex !== -1) {
      let nativeEvent = e.nativeEvent;
      let x = nativeEvent.offsetX;
      let y = nativeEvent.offsetY;
      let temp = [...pointList];
      temp.splice(dragPointIndex, 1, {
        x,
        y,
      });
      setPointList(temp);
      render();
    } else {
      if (isClosePath) {
        return false;
      }
      let nativeEvent = e.nativeEvent;
      let temp = {
        x: nativeEvent.offsetX,
        y: nativeEvent.offsetY,
      };
      setTmpPoint(temp);
      render();
    }
  };

  // 检测是否在某个顶点内
  let checkPointIndex = (x, y) => {
    let result = -1;
    // 遍历顶点绘制圆形路径，和上面的绘制顶点圆形的区别是这里不需要实际描边和填充，只需要路径
    pointList.forEach((item, index) => {
      ctx.beginPath();
      ctx.arc(item.x, item.y, 6, 0, 2 * Math.PI);
      // 检测是否在当前路径内
      if (ctx.isPointInPath(x, y)) {
        result = index;
      }
    });
    return result;
  };

  let onMousedown = (e) => {
    if (!isClosePath) {
      return;
    }
    setIsMousedown(true);
    let nativeEvent = e.nativeEvent;
    let x = nativeEvent.offsetX;
    let y = nativeEvent.offsetY;
    let index = checkPointIndex(x, y);
    setDragPointIndex(index);
  };


  let onMouseup = (e) => {
    if (!isClosePath) {
      return;
    }
    setIsMousedown(false);
  };

  let contextmenu = (event) => {
    event.preventDefault();
    let nativeEvent = event.nativeEvent;
    let x = nativeEvent.offsetX;
    let y = nativeEvent.offsetY;
    let index = checkPointIndex(x, y);
    let temp = [...pointList];
    
    temp.splice(index, 1, {
      x,
      y,
    });
    
    setPointList(temp);
    render();
  };

  return (
    <div
      className="canvas-box"
      style={{
        width: width + "px",
        height: height + "px",
        border: "1px solid red",
        position: "relative",
      }}
    >
      <canvas
        style={canvasStyle}
        width={width}
        height={height}
        ref={container}
        onMouseDown={onMousedown}
        onClickCapture={draw}
        onDoubleClickCapture={onDbClick}
        onMouseMove={mousemove}
        onContextMenu={contextmenu}
        onMouseUp={onMouseup}
      ></canvas>
    </div>
  );
};
export default DrawCanvas;
