import React, { useEffect, useState } from "react";
import DataTable from "@/components/DataTable";
import IconHandaler from "@/components/IconHandaler";
import MoreOption from "@/components/DataTableMoreOption";
import ListLayout from "@/components/ListLayout";
import AccessControl from "@/components/AccessControl";
import AddIcon from "@/assets/images/icon_add.svg?react";
import RefreshIcon from "@/assets/images/icon_refresh.svg?react";
import { useNavigate, useParams } from "react-router-dom";
import RingLoader from "react-spinners/RingLoader";
import {
  Tooltip,
  Grid,
  Box,
  Button,
  TextField,
  MenuItem,
  Dialog,
  DialogActions,
  Typography,
  DialogTitle,
} from "@mui/material";
import { toast } from "react-toastify";
import { getDeviceList, unBindDevice } from "@/services/device.js";
import CommonUtil from "@/util/CommonUtils";
import { useTranslation } from "react-i18next";
import { useStatePermission } from "@/hooks/user";
function Device() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // const permission = useStatePermission();
  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  ).permissions;

  const [isLoading, setIsLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [deviceId, setDeviceId] = useState("");
  const [page, setPage] = React.useState(0);
  const [totalRecords, setTotalRecords] = React.useState(1);
  const [serachSn, setSerachSn] = useState("");
  const [unbindOpen, setUnBindOpen] = useState(false);
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 5,
    id: "",
    sn: "",
    deviceModel: "",
    deviceType: "",
    accountEmail: "",
    companyId: "",
    deviceAlias: "",
    mac: "",
    siteId: "",
    zoneId: "",
    checkInOrOutType: "",
    ip: "",
    registrationDevice: "",
  });

  const menus = [
    {
      auth: "zt:dev:device_channel:save",
      label: `${t("LVLDV0040")}`,
      index: 2,
    },

    {
      auth: "zt:dev:device:delete",
      label: `${t("LVLDV0108")}`,
      index: 6,
    },
  ];

  const filteredMenus = menus?.filter((menu) => {
    return permission?.includes("*:*:*") || permission?.includes(menu.auth);
  });

  const columns = [
    {
      field: "outletName",
      headerName: `${t("LVLDB0004")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.outletName} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.outletName)}</span>
        </Tooltip>
      ),
    },
    {
      field: "sn",
      headerName: `${t("LVLDV0001")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.sn} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.sn)}</span>
        </Tooltip>
      ),
    },
    {
      field: "deviceAlias",
      headerName: `${t("LVLDV0002")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.deviceAlias} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.deviceAlias)}</span>
        </Tooltip>
      ),
    },
    {
      field: "deviceModel",
      headerName: `${t("LVLDV0003")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.deviceModel} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.deviceModel)}</span>
        </Tooltip>
      ),
    },
    {
      field: "dmsDeviceType",
      headerName: `${t("LVLDV0004")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.dmsDeviceType} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.dmsDeviceType)}</span>
        </Tooltip>
      ),
    },

    {
      field: "siteId",
      headerName: `${t("LVLDV0005")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e.row.outletName} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.outletName)}</span>
        </Tooltip>
      ),
    },
    {
      field: "status",
      headerName: `${t("LVLDV0006")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => {
        return (
          <Tooltip
            title={e.row.status == "1" ? t("LVLDV0091") : t("LVLDV0092")}
            arrow
            placement="bottom">
            <span
              style={{
                color: e.row.status == "1" ? "#7ac143" : "#ff0000",
                fontWeight: "bold",
              }}>
              {CommonUtil.formatLongText(
                e.row.status == "1" ? t("LVLDV0091") : t("LVLDV0092")
              )}
            </span>
          </Tooltip>
        );
      },
    },
    {
      field: "mainOrSub",
      headerName: `${t("LVLDV0109")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => {
        return (
          <Tooltip title={e.row.mainOrSub} arrow placement="bottom">
            <span>
              {CommonUtil.formatLongText(
                e.row.mainOrSub == "0" ? t("主设备") : t("子设备")
              )}
            </span>
          </Tooltip>
        );
      },
    },
    {
      headerName: `${t("LVLDV0007")}`,
      sortable: false,
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (e) => {
        return (
          <IconHandaler>
            <MoreOption
              data={filteredMenus.filter((menu) => {
                if (e.row.deviceModel === "T1") {
                  return menu.index !== 5 || menu.index === 6;
                } else if (e.row.deviceModel === "PC") {
                  return menu.index !== 5 && menu.index !== 6;
                } else {
                  return menu;
                }
              })}
              handleSelect={(option) => handleActions(option, e)}
            />
          </IconHandaler>
        );
      },
    },
  ];

  const loadData = () => {
    setIsLoading(true);
    let params = {
      ...filters,
      applicationCode: "ZT",
    };
    getDeviceList(params)
      .then((res) => {
        if (res.code === "DMSE9999") {
          toast.warning(res.data.message);
        }
        if (res?.data) {
          setRecords(res?.data?.data || []);
          setTotalRecords(res?.data?.total);
          setPage(res?.data?.page);
          setRowsPerPage(res?.data?.pageSize);
          setIsLoading(false);
        }
      })
      .catch((err) => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    loadData();
  }, [filters]);

  const handlePageChange = (e) => {
    setFilters({ ...filters, page: e + 1 });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...filters,
      page: filters.page,
      pageSize: e,
    });
  };

  const handleActions = (action, data) => {
    let row = data.row;
    if (action === 1) {
      //后续需要添加参数id查后端回显，暂时先写死
      navigate("/device/edit/" + row.sn, {
        state: row,
      });
    } else if (action === 2) {
      unbindDevice(data.row.id, data.row.siteId);
    } else if (action === 6) {
      navigate("/device/channel/setting", {
        state: row,
      });
    }
  };

  const getRowId = (device) => device.id;

  const handleSubmit = () => {
    setIsLoading(true);
    getDeviceList({ page: 1, pageSize: filters.pageSize, sn: serachSn })
      .then((res) => {
        if (res.code === "DMSE9999") {
          toast.warning(res.data.message);
        }
        if (res?.data) {
          setRecords(res?.data?.data || []);
          setTotalRecords(res?.data?.total);
          setPage(res?.data?.page);
          setRowsPerPage(res?.data?.pageSize);
          setIsLoading(false);
        }
      })
      .catch((err) => {
        setIsLoading(false);
      });
  };

  const handleReset = () => {
    setSerachSn("");
    loadData();
  };

  const unbindDevice = (id) => {
    setUnBindOpen(true);
    setDeviceId(id);
  };

  return (
    <React.Fragment>
      <ListLayout
        navigateBack={false}
        title={t("MENU10")}
        // toolbarProps={toolbarProps}
      >
        <Grid
          display={"flex"}
          sx={{
            background: "#FFF",
            height: "100px",
            borderRadius: "15px",
          }}>
          <Box
            sx={{
              margin: "30px 30px",
            }}>
            <TextField
              size="small"
              label={t("LVLDV0077")}
              placeholder={t("LVLDV0078")}
              value={serachSn}
              onChange={(e) => setSerachSn(e.target.value)}></TextField>
          </Box>

          <Box sx={{ margin: "35px 35px" }}>
            <Button
              variant="contained"
              sx={{ marginLeft: 1, p: 0.5 }}
              style={{
                height: "28px",
              }}
              onClick={handleSubmit}>
              {t("LVLGF0009")}
            </Button>

            <Button
              style={{
                height: "28px",
              }}
              sx={{ marginLeft: 1, p: 0.5 }}
              variant="outlined"
              onClick={handleReset}>
              {t("LVLGF0008")}
            </Button>
          </Box>
        </Grid>
        <Grid display={"flex"} justifyContent={"flex-end"} marginTop={2}>
          {/* <AccessControl authorization={"1018"}> */}
          {/* <Box
            item
            pr={2}
            onClick={() => {
              navigate("/device/add");
            }}>
            <AddIcon width={"35"} height={"35"} className="pointer" />
          </Box> */}
          {/* </AccessControl> */}
          <Box item pr={2}>
            <RefreshIcon
              width={"35"}
              height={"35"}
              className="pointer"
              onClick={() => loadData()}
            />
          </Box>
        </Grid>
        {isLoading ? (
          <RingLoader
            color={"#597ef7"}
            loading={isLoading}
            cssOverride={{
              display: "block",
              margin: "10% auto",
              borderColor: "#b37feb",
            }}
            size={60}
            speedMultiplier={3}
            aria-label="Loading Spinner"
            data-testid="loader"
          />
        ) : (
          // <AccessControl authorization={"1016"}>
          <DataTable
            columns={columns}
            rows={records}
            onSelection={() => console.log()}
            getRowId={getRowId}
            page={filters.page - 1}
            totalRecords={totalRecords}
            rowsPerPage={filters.pageSize}
            onPageChange={(pn) => handlePageChange(pn)}
            onPageSizeChange={(ps) => handlePageSize(ps)}
          />
          // </AccessControl>
        )}

        <TitleDialog
          unbindOpen={unbindOpen}
          setUnBindOpen={setUnBindOpen}
          loadData={loadData}
          deviceId={deviceId}></TitleDialog>
      </ListLayout>
    </React.Fragment>
  );
}

export default Device;

const TitleDialog = ({ unbindOpen, setUnBindOpen, deviceId, loadData }) => {
  const { t } = useTranslation();

  const handlerSubmit = () => {
    unBindDevice(deviceId).then((res) => {
      toast.success(t("LVLDV0057"));
      setUnBindOpen(false);
      loadData();
    });
  };

  return (
    <React.Fragment>
      <Dialog open={unbindOpen} onClose={() => setUnBindOpen(false)}>
        <DialogTitle>
          <Typography variant="h4" component="p">
            {t("LVLDV0058")}
          </Typography>
        </DialogTitle>

        <DialogActions>
          <Button variant="contained" size="large" onClick={handlerSubmit}>
            {t("LVLOT0016")}
          </Button>
          <Button
            variant="outlined"
            size="large"
            onClick={() => setUnBindOpen(false)}>
            {t("LVLOT0015")}
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
};
