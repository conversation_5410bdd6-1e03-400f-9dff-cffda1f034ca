//列 数据组件

import { Avatar, Tooltip, Popover, Button } from "@mui/material";
import CommonUtil from "@/util/CommonUtils";
import { useState, useEffect, useMemo } from "react";
import AccessControl from "@/components/AccessControl";
import React from "react";
export const getcolumns = (t, handleImageClick, selectedValue) => {
  const [selectList, setSelectList] = useState(1);
  useEffect(() => {
    // const handleSelectedValue = (value) => {
    //   const options = {
    //     "1897586872701005826": 1,
    //     "1897587020244037633": 2,
    //     "1897587221688070146": 3,
    //   };
    //   setSelectList(options[value] || 4);
    // };
    // handleSelectedValue(selectedValue);

    setSelectList(selectedValue);
  }, [selectedValue]);

  const renderTooltipCell = (value) => (
    <Tooltip title={value} arrow placement="bottom">
      <span>{CommonUtil.formatLongText(value)}</span>
    </Tooltip>
  );

  const getEventPhotoUrl = (row) => {
    if (row !== null) {
      return row.imagePath;
    } else if (row !== null) {
      return row.imagePath;
    } else {
      return row.imagePath;
    }
  };

  /**
   * 初始字段 所有类型都拥有
   */
  let columns = [
    {
      field: "eventTime",
      headerName: t("RTMT0004"),
      width: 130,
      headerAlign: "center",
      align: "center",
      sortable: false,
      renderCell: (e) => {
        const eventTime = e.row?.eventTime || "";
        return renderTooltipCell(eventTime);
      },
    },
    {
      field: "recognitionTarget",
      headerName: t("RTMT0007"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      sortable: false,
      renderCell: (e) => {
        const [eventType, setEventType] = useState("");
        useEffect(() => {
          if (e.row !== null) {
            setEventType(e.row?.eventLogVO?.recognitionTarget);
          } else {
            setEventType("");
          }
        }, [e.row]);
        return renderTooltipCell(eventType);
      },
    },

    {
      field: "trackId",
      headerName: t("RTMT0020"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      sortable: false,
      renderCell: (e) => {
        const [trackId, setTrackId] = useState(0);
        useEffect(() => {
          if (e.row !== null) {
            setTrackId(e.row.eventLogVO?.trackId);
          } else {
            setTrackId("");
          }
        }, [e.row]);
        return renderTooltipCell(trackId);
      },
    },
  ];

  /**
   * Crocess the line 应该拥有的字段
   */

  if (selectList == 3) {
    columns.push(
      {
        field: "eventType",
        headerName: t("RTMT0027"),
        flex: 1,
        align: "center",
        headerAlign: "center",
        sortable: false,
        renderCell: (e) => {
          const eventType = e.row?.eventLogVO?.eventType || "";
          return renderTooltipCell(eventType);
        },
      },
      {
        field: "height",
        headerName: t("RTMT0026"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const height = e.row.eventLogVO?.height || "";
          return renderTooltipCell(height);
        },
      }
    );
  }

  /**
   * face 应该有的字段
   */
  if (selectList == 2) {
    columns.push({
      field: "maskStatus",
      headerName: t("RTMT0019"),
      flex: 1,
      sortable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (e) => {
        const maskStatus = e.row?.maskStatus || "";
        return renderTooltipCell(maskStatus);
      },
    });
  }

  /**
   * Body 应该有的字段
   */
  if (selectList == 1) {
    columns.push(
      {
        field: "upperColor",
        headerName: t("RTMT0025"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const upperColor = e.row?.eventLogVO?.upperColor || "";
          return renderTooltipCell(upperColor);
        },
      },

      {
        field: "lowerColor",
        headerName: t("RTMT0022"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const upperTexture = e.row?.eventLogVO?.lowerColor || "";
          return renderTooltipCell(upperTexture);
        },
      },
      {
        field: "rideBike",
        headerName: t("RTMT0023"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const rideBike = e.row?.eventLogVO?.rideBike || "";
          return renderTooltipCell(rideBike);
        },
      },
      {
        field: "umbrella",
        headerName: t("RTMT0024"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const umbrella = e.row?.eventLogVO?.umbrella || "";
          return renderTooltipCell(umbrella);
        },
      },
      {
        field: "bag",
        headerName: t("RTMT0028"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const bag = e.row?.eventLogVO?.bag || "";
          return renderTooltipCell(bag);
        },
      },
      {
        field: "channelNum",
        headerName: t("RTMT0021"),
        flex: 1,
        align: "center",
        headerAlign: "center",
        sortable: false,
        renderCell: (e) => {
          const channelNum = e.row?.eventLogVO?.channelNum || "";
          return renderTooltipCell(channelNum);
        },
      }
    );
  }

  /**
   * face 、 Body共同拥有的字段
   */
  if (selectList == 1 || selectList == 2) {
    columns.push(
      {
        field: "age",
        headerName: t("RTMT0015"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const age = e.row?.eventLogVO?.age || "";
          return renderTooltipCell(age);
        },
      },
      {
        field: "gender",
        headerName: t("RTMT0016"),
        flex: 1,
        sortable: false,
        headerAlign: "center",
        align: "center",
        renderCell: (e) => {
          const gender = e.row?.eventLogVO?.gender || "";
          return renderTooltipCell(gender);
        },
      },
      {
        field: "glasses",
        headerName: t("RTMT0017"),
        flex: 1,
        sortable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (e) => {
          const glasses = e.row?.eventLogVO?.glasses || "";
          return renderTooltipCell(glasses);
        },
      },
      {
        field: "hat",
        headerName: t("RTMT0018"),
        flex: 1,
        sortable: false,
        headerAlign: "center",
        align: "center",
        renderCell: (e) => {
          const hat = e.row?.eventLogVO?.hat || "";
          return renderTooltipCell(hat);
        },
      },
      {
        field: "scene",
        headerName: t("RTMT0032"),
        flex: 1,
        sortable: false,
        headerAlign: "center",
        align: "center",
        renderCell: (e) => {
          const scene = e.row?.eventLogVO?.scene || "NA";
          return renderTooltipCell(scene);
        },
      }
    );
  }

  return columns;
};
