/* eslint-disable react-hooks/rules-of-hooks */
/**
 * @param {string} pathname 当前路由路径
 * @param {object} meta 当前路由自定义meta字段
 * @return {string} 需要跳转到其他页时，就返回一个该页的path路径，或返回resolve该路径的promise对象
 */




import i18n from "i18next";

const onRouteBefore = async ({ pathname, meta }) => {

  // 示例：动态修改页面title
  if (meta.i18n !== undefined) {
    document.title = i18n.t("menu." + meta?.i18n);
  }



};

export default onRouteBefore;
