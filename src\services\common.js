import request from "@/util/request";

/**
 *   获取路由菜单接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getUserMenus = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/auth/resource/menu`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取门店下拉列表接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getOutletList = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/query/list`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取门店分页列表接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getOutletPageList = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/query/page`,
    method: "GET",
    params: params,
  });
};



/**
 *  获取按钮级权限
 */

export const getAuthButton = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/auth/application/${params}`,
    method: "POST",

  });
};