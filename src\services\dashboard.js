import request from "@/util/request";

/**
 *  获取PeopleCount 看板接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getPeopleCounting = (params) => {
  return request({
    url: `zt/v1/people_counting/query`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取Demographic 看板接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getDemographic = (params) => {
  return request({
    url: `zt/v1/demographic/query`,
    method: "GET",
    params: params,
  });
};

export const exportPeopleCounting = (params) => {
  return request({
    url: `zt/v1/people_counting/export`,
    method: "GET",
    params: params,
    responseType: "blob",
  });
};
