import request from "@/util/request";
const baseProfixURI = `${import.meta.env.VITE_APICODE}`;
/**
 *  获取Device Channel 接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getChannelType = (id) => {
  return request({
    url: `zt/v1/device/channel/query/${id}`,
    method: "GET",
  });
};

/**
 *  获取设备列表
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getDeviceList = (params) => {
  return request({
    url: `/global/v1/device/query/page`,
    method: "GET",
    params: params,
  });
};

/**
 *  解绑设备
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const unBindDevice = (ids) => {
  return request({
    url: `zt/v1/device/${ids}`,
    method: "DELETE",
  });
};

/**
 *  保存通道
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const addChannel = (params) => {
  return request({
    url: `zt/v1/device/channel`,
    method: "POST",
    data: params,
  });
};
