import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setMenuList } from "../reducers/menu";
export const useMenuInfo = () =>
  useSelector((store) => {
    return store.menu.menuList;
  });

export function useDispatchMenu() {
  const dispatch = useDispatch();
  const stateSetMenu = useCallback(
    (useMenu) => dispatch(setMenuList(useMenu)),
    [dispatch]
  );
  return { stateSetMenu };
}
