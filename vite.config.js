import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import qiankun from "vite-plugin-qiankun";
import path from "path";
import svgr from "vite-plugin-svgr";
import { name } from "./package.json";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import tailwindcss from 'tailwindcss'
import { setupUnPluginIcon } from "./build/icon.js";
import { wrapperEnv } from "./src/util/getEnv";
import autoprefixer from "autoprefixer";
export default defineConfig((mode) => {
  const env = loadEnv(mode.mode, process.cwd());
  const viteEnv = wrapperEnv(env);
  return {
    base:
      process.env.NODE_ENV === "development" ? "./" : "/retail-ai-app",
    plugins: [
      react(),
      qiankun(name, {
        useDevMode: process.env.NODE_ENV === "development", // 仅开发模式启用,
      }),
      setupUnPluginIcon(viteEnv),
      svgr({
        svgrOptions: {},
      }),

      cssInjectedByJsPlugin(),
    ],


    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },



    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@c": path.resolve(__dirname, "./src/components"),
        "@p": path.resolve(__dirname, "./src/pages"),
        "@l": path.resolve(__dirname, "./src/layout"),
        "@r": path.resolve(__dirname, "./src/router"),
        "@s": path.resolve(__dirname, "./src/service"),
        "@u": path.resolve(__dirname, "./src/utils"),
        "@a": path.resolve(__dirname, "./src/assets"),
      },

    },

    server: {
      port: 8082,
      host: "0.0.0.0",
      cors: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      origin: "http://localhost:8082",
      proxy: {
        "/dev": {
          // 推荐不要直接修改下面的地址，查看同级目录下的local-env.js.sample文件介绍
          // target: "http://**********:9090",
          // target: "http://**********:9090",
          // target: "http://***********:9090",
          target: "http://**********:9090",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev/, ""),
        },
      },
    },

    build: process.env.QIANKUN
      ? {
        target: "esnext",
        cssCodeSplit: false,
        rollupOptions: {
          output: {
            format: "umd",
            name: name,
            entryFileNames: `${name}-[name].js`,
            chunkFileNames: `${name}-[name].js`,
            assetFileNames: `${name}-[name].[ext]`,
            globals: {
              react: "React",
              "react-dom": "ReactDOM",
            },
          },
        },
      }
      : undefined,
  }
});
